/**
 * cPanel Environment Variables Fix
 *
 * This script ensures environment variables are properly loaded in cPanel hosting
 * Add this to the top of your server.js file or create a separate env loader
 */

const fs = require('fs');
const path = require('path');

function loadEnvironmentVariables() {
  console.log('🔧 Loading environment variables for cPanel...');

  // Try multiple locations for .env file
  const possibleEnvPaths = [
    path.join(process.cwd(), '.env'),
    path.join(__dirname, '.env'),
    path.join(process.cwd(), '..', '.env'), // Parent directory
    '/home/<USER>/public_html/.env', // Typical cPanel path (replace username)
  ];

  let envLoaded = false;

  for (const envPath of possibleEnvPaths) {
    if (fs.existsSync(envPath)) {
      console.log(`✅ Found .env file at: ${envPath}`);

      try {
        // Read and parse .env file manually
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envLines = envContent.split('\n');

        for (const line of envLines) {
          const trimmedLine = line.trim();
          if (trimmedLine && !trimmedLine.startsWith('#')) {
            const [key, ...valueParts] = trimmedLine.split('=');
            if (key && valueParts.length > 0) {
              let value = valueParts.join('=');

              // Remove quotes if present
              if ((value.startsWith('"') && value.endsWith('"')) ||
                  (value.startsWith("'") && value.endsWith("'"))) {
                value = value.slice(1, -1);
              }

              // Set environment variable if not already set
              if (!process.env[key.trim()]) {
                process.env[key.trim()] = value;
                console.log(`   Set ${key.trim()}`);
              }
            }
          }
        }

        envLoaded = true;
        break;
      } catch (error) {
        console.error(`❌ Error reading .env file at ${envPath}:`, error.message);
      }
    }
  }

  if (!envLoaded) {
    console.log('⚠️  No .env file found. Checking for environment variables...');
  }

  // Verify critical environment variables
  const requiredVars = ['DATABASE_URL', 'NEXTAUTH_SECRET'];
  const missingVars = [];

  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    } else {
      console.log(`✅ ${varName} is set`);
    }
  }

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars);

    // Set fallback DATABASE_URL if missing (for debugging)
    if (!process.env.DATABASE_URL) {
      console.log('🔧 Setting fallback DATABASE_URL...');
      process.env.DATABASE_URL = "*******************************************************************************************************/postgres?sslmode=require";
    }

    if (!process.env.NEXTAUTH_SECRET) {
      console.log('🔧 Setting fallback NEXTAUTH_SECRET...');
      process.env.NEXTAUTH_SECRET = "fallback-secret-change-in-production";
    }
  }

  console.log('✅ Environment variables loaded successfully');
}

// Export for use in other files
module.exports = { loadEnvironmentVariables };

// Auto-load if this file is run directly
if (require.main === module) {
  loadEnvironmentVariables();
}
