"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ImageUpload } from "@/components/image-upload"
import { addPageContent, updatePageContent, type PageContent } from "@/lib/api-client"

interface PageContentFormProps {
  initialData?: PageContent
  mode?: "add" | "edit"
  onSuccess?: () => void
}

const PAGE_OPTIONS = [
  { value: "homepage", label: "Homepage" },
  { value: "research", label: "Research Page" },
  { value: "about", label: "About Us" },
  { value: "careers", label: "Careers" },
  { value: "contact", label: "Contact" },
  { value: "privacy", label: "Privacy Policy" },
  { value: "terms", label: "Terms of Service" },
  { value: "cookies", label: "Cookie Policy" },
]

const SECTION_OPTIONS = {
  homepage: [
    { value: "hero", label: "Hero Section" },
    { value: "features", label: "Features Section" },
    { value: "about", label: "About Section" },
    { value: "cta", label: "Call to Action" },
  ],
  research: [
    { value: "hero", label: "Research Hero" },
    { value: "areas", label: "Research Areas" },
    { value: "methodology", label: "Methodology" },
  ],
  about: [
    { value: "hero", label: "About Hero" },
    { value: "mission", label: "Mission" },
    { value: "story", label: "Our Story" },
    { value: "values", label: "Values" },
    { value: "team", label: "Team Section" },
  ],
  careers: [
    { value: "hero", label: "Careers Hero" },
    { value: "culture", label: "Company Culture" },
    { value: "benefits", label: "Benefits" },
    { value: "openings", label: "Job Openings" },
  ],
  contact: [
    { value: "hero", label: "Contact Hero" },
    { value: "info", label: "Contact Information" },
    { value: "form", label: "Contact Form" },
  ],
  privacy: [
    { value: "content", label: "Privacy Content" },
  ],
  terms: [
    { value: "content", label: "Terms Content" },
  ],
  cookies: [
    { value: "content", label: "Cookie Content" },
  ],
}

export function PageContentForm({ initialData, mode = "add", onSuccess }: PageContentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    page_id: initialData?.page_id || "homepage",
    section_id: initialData?.section_id || "hero",
    title: initialData?.title || "",
    content: initialData?.content || "",
    image: initialData?.image || "",
    order_index: initialData?.order_index || 1,
    is_active: initialData?.is_active ?? true,
  })
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      if (mode === "edit" && initialData) {
        const success = await updatePageContent(initialData.id, formData)
        if (success) {
          toast({
            title: "Content updated",
            description: "The page content has been updated successfully",
          })
          onSuccess?.()
        } else {
          throw new Error("Failed to update content")
        }
      } else {
        await addPageContent(formData)
        toast({
          title: "Content added",
          description: "The page content has been added successfully",
        })
        onSuccess?.()
        // Reset form for add mode
        setFormData({
          page_id: "homepage",
          section_id: "hero",
          title: "",
          content: "",
          image: "",
          order_index: 1,
          is_active: true,
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode} page content`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handlePageChange = (pageId: string) => {
    updateField("page_id", pageId)
    // Reset section when page changes
    const firstSection = SECTION_OPTIONS[pageId as keyof typeof SECTION_OPTIONS]?.[0]?.value || "hero"
    updateField("section_id", firstSection)
  }

  const currentSections = SECTION_OPTIONS[formData.page_id as keyof typeof SECTION_OPTIONS] || []

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label>Page *</Label>
          <Select value={formData.page_id} onValueChange={handlePageChange}>
            <SelectTrigger className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50">
              <SelectValue placeholder="Select a page" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-purple-500/20">
              {PAGE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Section *</Label>
          <Select value={formData.section_id} onValueChange={(value) => updateField("section_id", value)}>
            <SelectTrigger className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50">
              <SelectValue placeholder="Select a section" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-purple-500/20">
              {currentSections.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="title">Title *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => updateField("title", e.target.value)}
            placeholder="Section title"
            required
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="order_index">Display Order</Label>
          <Input
            id="order_index"
            type="number"
            min="1"
            value={formData.order_index}
            onChange={(e) => updateField("order_index", parseInt(e.target.value) || 1)}
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Content *</Label>
        <Textarea
          id="content"
          value={formData.content}
          onChange={(e) => updateField("content", e.target.value)}
          placeholder="Enter the content for this section..."
          required
          rows={6}
          className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
        <p className="text-xs text-gray-400">
          This content will be displayed in the {formData.section_id} section of the {formData.page_id} page.
        </p>
      </div>

      <ImageUpload
        label="Section Image (Optional)"
        value={formData.image}
        onChange={(value) => updateField("image", value)}
        placeholder="Add an image for this section (optional - will use placeholder if empty)"
      />

      <div className="flex items-center space-x-2">
        <Switch
          id="is_active"
          checked={formData.is_active}
          onCheckedChange={(checked) => updateField("is_active", checked)}
        />
        <Label htmlFor="is_active">Active (visible on website)</Label>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {isSubmitting ? "Saving..." : mode === "edit" ? "Update Content" : "Add Content"}
        </Button>
      </div>
    </form>
  )
}
