"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { OptimizedImage } from "@/components/optimized-image"

export default function PressPage() {
  const pressReleases = [
    {
      id: 1,
      title: "Interlock Announces Breakthrough in Neural Associative Modeling",
      date: "April 15, 2024",
      summary:
        "Interlock researchers have developed a new approach to neural associative modeling that demonstrates a 40% improvement in concept formation compared to previous methods.",
      link: "#",
    },
    {
      id: 2,
      title: "Interlock Secures $25M Series A Funding",
      date: "March 2, 2024",
      summary:
        "Funding will accelerate research in human-inspired AI and expand the team across research, engineering, and product development.",
      link: "#",
    },
    {
      id: 3,
      title: "Interlock Partners with Stanford University for Cognitive AI Research",
      date: "February 10, 2024",
      summary:
        "Partnership will focus on advancing contextual understanding and emotional intelligence in AI systems through collaborative research.",
      link: "#",
    },
  ]

  const mediaFeatures = [
    {
      id: 1,
      title: "The Future of Human-Inspired AI",
      publication: "Tech Innovators",
      date: "March 28, 2024",
      summary: "An in-depth look at how Interlock is revolutionizing AI by modeling human cognitive processes.",
      link: "#",
    },
    {
      id: 2,
      title: "AI That Thinks Like We Do",
      publication: "Future Science",
      date: "February 15, 2024",
      summary:
        "Interview with Interlock's Chief Research Officer on the challenges and breakthroughs in creating more human-like AI.",
      link: "#",
    },
    {
      id: 3,
      title: "Startups to Watch: Interlock Reimagines AI",
      publication: "Venture Weekly",
      date: "January 20, 2024",
      summary: "Profile of Interlock as one of the most promising AI startups of 2024.",
      link: "#",
    },
  ]

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-5xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Press & Media
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          The latest news and media coverage about Interlock and our research in human-inspired AI.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Interlock Press"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute inset-0 z-20 flex items-center justify-center">
            <div className="text-center p-6">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Media Inquiries</h2>
              <p className="text-xl text-gray-200 mb-6">
                For press inquiries, please contact our media relations team.
              </p>
              <Button className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700">
                Contact Press Team
              </Button>
            </div>
          </div>
        </div>

        <h2 className="text-3xl font-bold text-white mb-6">Press Releases</h2>
        <div className="space-y-6 mb-12">
          {pressReleases.map((release) => (
            <Card key={release.id} className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-xl text-cyan-400">{release.title}</CardTitle>
                <CardDescription className="text-gray-400">{release.date}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">{release.summary}</p>
              </CardContent>
              <CardFooter>
                <Link href={release.link}>
                  <Button variant="outline" className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10">
                    Read Full Release
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>

        <h2 className="text-3xl font-bold text-white mb-6">Media Coverage</h2>
        <div className="space-y-6">
          {mediaFeatures.map((feature) => (
            <Card key={feature.id} className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-xl text-cyan-400">{feature.title}</CardTitle>
                <CardDescription className="text-gray-400">
                  {feature.publication} • {feature.date}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">{feature.summary}</p>
              </CardContent>
              <CardFooter>
                <Link href={feature.link}>
                  <Button variant="outline" className="border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10">
                    Read Article
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </motion.div>
    </div>
  )
}
