@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Cyberpunk text effects */
@layer utilities {
  .text-cyberpunk {
    @apply font-orbitron font-black tracking-wider uppercase;
    text-shadow:
      0 0 5px rgba(34, 211, 238, 0.5),
      0 0 10px rgba(34, 211, 238, 0.3),
      0 0 15px rgba(34, 211, 238, 0.2);
  }

  .text-cyberpunk-glow {
    @apply text-cyberpunk;
    animation: cyberpunk-glow 2s ease-in-out infinite alternate;
  }

  .text-glitch {
    position: relative;
  }

  .text-glitch::before,
  .text-glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .text-glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #ff00ff;
    z-index: -1;
  }

  .text-glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #00ffff;
    z-index: -2;
  }
}

@keyframes cyberpunk-glow {
  from {
    text-shadow:
      0 0 5px rgba(34, 211, 238, 0.5),
      0 0 10px rgba(34, 211, 238, 0.3),
      0 0 15px rgba(34, 211, 238, 0.2);
  }
  to {
    text-shadow:
      0 0 10px rgba(34, 211, 238, 0.8),
      0 0 20px rgba(34, 211, 238, 0.6),
      0 0 30px rgba(34, 211, 238, 0.4);
  }
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 0);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, 0);
  }
}

/* Custom cyberpunk styles */
.dark {
  --background: 240 20% 2%;
  --foreground: 210 40% 98%;
  --card: 240 17% 5%;
  --card-foreground: 210 40% 98%;
  --border: 240 13% 12%;
  --input: 240 13% 12%;
  --ring: 142 71% 45%;
  --primary: 142 71% 45%;
  --primary-foreground: 144 100% 11%;
  --secondary: 240 19% 10%;
  --secondary-foreground: 210 40% 98%;
  --accent: 291 70% 50%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --muted: 240 19% 10%;
  --muted-foreground: 240 5% 65%;
  --popover: 240 17% 5%;
  --popover-foreground: 210 40% 98%;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: rgba(124, 58, 237, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(124, 58, 237, 0.7);
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}
