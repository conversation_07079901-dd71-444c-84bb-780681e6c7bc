#!/usr/bin/env node

/**
 * Create deployment ZIP file for cPanel upload
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('📦 Creating deployment ZIP file...\n');

const DEPLOYMENT_DIR = 'cpanel-deployment';
const ZIP_NAME = 'interlock-ai-cpanel-deployment.zip';

// Check if deployment directory exists
if (!fs.existsSync(DEPLOYMENT_DIR)) {
  console.error('❌ Deployment directory not found. Run "npm run deploy:prepare" first.');
  process.exit(1);
}

// Remove existing zip file if it exists
if (fs.existsSync(ZIP_NAME)) {
  fs.unlinkSync(ZIP_NAME);
  console.log('🗑️  Removed existing ZIP file');
}

// Create a file to stream archive data to
const output = fs.createWriteStream(ZIP_NAME);
const archive = archiver('zip', {
  zlib: { level: 9 } // Sets the compression level
});

// Listen for all archive data to be written
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`\n✅ ZIP file created successfully!`);
  console.log(`📁 File: ${ZIP_NAME}`);
  console.log(`📊 Size: ${sizeInMB} MB`);
  console.log(`📦 Total files: ${archive.pointer()} bytes`);
  console.log('\n🚀 Ready for cPanel upload!');
  console.log('\nNext steps:');
  console.log('1. Upload this ZIP file to your cPanel File Manager');
  console.log('2. Extract it in your domain\'s public_html directory');
  console.log('3. Follow the instructions in DEPLOYMENT_INSTRUCTIONS.md');
});

// Handle warnings (e.g., stat failures and other non-blocking errors)
archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('⚠️  Warning:', err.message);
  } else {
    throw err;
  }
});

// Handle errors
archive.on('error', function(err) {
  console.error('❌ Error creating ZIP:', err.message);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

console.log('📁 Adding files to ZIP...');

// Add the entire deployment directory to the zip
archive.directory(DEPLOYMENT_DIR, false);

// Finalize the archive (i.e., we are done appending files but streams have to finish yet)
archive.finalize();
