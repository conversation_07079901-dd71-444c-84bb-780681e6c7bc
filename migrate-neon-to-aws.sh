#!/bin/bash

# =====================================================
# Neon to AWS RDS PostgreSQL Migration Script
# Complete database migration with data export/import
# =====================================================

echo "🚀 Neon to AWS RDS PostgreSQL Migration"
echo "========================================"

# Configuration
NEON_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
AWS_RDS_URL="*******************************************************************************************************/postgres?sslmode=require"

# File names
SCHEMA_DUMP="neon_schema_dump.sql"
DATA_DUMP="neon_data_dump.sql"
FULL_DUMP="neon_full_dump.sql"

echo "📋 Migration Steps:"
echo "1. Export schema and data from Neon"
echo "2. Create schema on AWS RDS"
echo "3. Import data to AWS RDS"
echo "4. Verify migration"
echo ""

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists pg_dump; then
    echo "❌ pg_dump not found. Please install PostgreSQL client tools."
    echo "   Ubuntu/Debian: sudo apt-get install postgresql-client"
    echo "   CentOS/RHEL: sudo yum install postgresql"
    echo "   macOS: brew install postgresql"
    exit 1
fi

if ! command_exists psql; then
    echo "❌ psql not found. Please install PostgreSQL client tools."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Step 1: Export from Neon
echo "📤 Step 1: Exporting from Neon PostgreSQL..."

echo "   Exporting schema only..."
pg_dump "$NEON_URL" --schema-only --no-owner --no-privileges > "$SCHEMA_DUMP"
if [ $? -eq 0 ]; then
    echo "   ✅ Schema export successful"
else
    echo "   ❌ Schema export failed"
    exit 1
fi

echo "   Exporting data only..."
pg_dump "$NEON_URL" --data-only --no-owner --no-privileges > "$DATA_DUMP"
if [ $? -eq 0 ]; then
    echo "   ✅ Data export successful"
else
    echo "   ❌ Data export failed"
    exit 1
fi

echo "   Exporting complete database..."
pg_dump "$NEON_URL" --no-owner --no-privileges > "$FULL_DUMP"
if [ $? -eq 0 ]; then
    echo "   ✅ Full database export successful"
else
    echo "   ❌ Full database export failed"
    exit 1
fi

echo ""

# Step 2: Test AWS RDS Connection
echo "🔌 Step 2: Testing AWS RDS connection..."

# Extract connection details for testing
AWS_HOST=$(echo "$AWS_RDS_URL" | sed -n 's/.*@\([^:]*\):.*/\1/p')
AWS_PORT=$(echo "$AWS_RDS_URL" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')

echo "   Testing connection to $AWS_HOST:$AWS_PORT..."

# Test basic connectivity
timeout 10 bash -c "</dev/tcp/$AWS_HOST/$AWS_PORT" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "   ✅ Network connectivity successful"
else
    echo "   ❌ Cannot connect to AWS RDS. Check:"
    echo "      - Security group settings"
    echo "      - RDS instance status"
    echo "      - Network connectivity"
    exit 1
fi

# Test PostgreSQL connection
echo "   Testing PostgreSQL authentication..."
psql "$AWS_RDS_URL" -c "SELECT version();" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "   ✅ PostgreSQL connection successful"
else
    echo "   ❌ PostgreSQL connection failed. Check:"
    echo "      - Database credentials"
    echo "      - Database name"
    echo "      - SSL settings"
    echo ""
    echo "   Please update the AWS_RDS_URL variable with correct credentials:"
    echo "   AWS_RDS_URL=\"postgresql://username:<EMAIL>:5432/database_name?sslmode=require\""
    exit 1
fi

echo ""

# Step 3: Create schema on AWS RDS
echo "📋 Step 3: Creating schema on AWS RDS..."

echo "   Using custom schema script..."
psql "$AWS_RDS_URL" -f "aws-rds-migration-schema.sql"
if [ $? -eq 0 ]; then
    echo "   ✅ Schema creation successful"
else
    echo "   ❌ Schema creation failed"
    echo "   Trying with exported schema..."
    psql "$AWS_RDS_URL" -f "$SCHEMA_DUMP"
    if [ $? -eq 0 ]; then
        echo "   ✅ Exported schema import successful"
    else
        echo "   ❌ Schema import failed"
        exit 1
    fi
fi

echo ""

# Step 4: Import data to AWS RDS
echo "📥 Step 4: Importing data to AWS RDS..."

echo "   Importing seed data..."
psql "$AWS_RDS_URL" -f "aws-rds-migration-data.sql"
if [ $? -eq 0 ]; then
    echo "   ✅ Seed data import successful"
else
    echo "   ⚠️  Seed data import had issues (may be normal if data already exists)"
fi

echo "   Importing exported data..."
psql "$AWS_RDS_URL" -f "$DATA_DUMP"
if [ $? -eq 0 ]; then
    echo "   ✅ Data import successful"
else
    echo "   ⚠️  Data import had issues (may be normal for duplicate data)"
fi

echo ""

# Step 5: Verify migration
echo "🔍 Step 5: Verifying migration..."

echo "   Checking table counts..."
psql "$AWS_RDS_URL" -c "
SELECT
    schemaname,
    tablename,
    (SELECT COUNT(*) FROM pg_class WHERE relname = tablename) as table_exists,
    (SELECT n_tup_ins FROM pg_stat_user_tables WHERE relname = tablename) as row_count
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;
"

echo ""
echo "   Checking specific Interlock AI tables..."
psql "$AWS_RDS_URL" -c "
SELECT 'research_areas' as table_name, COUNT(*) as records FROM research_areas
UNION ALL
SELECT 'feature_cards', COUNT(*) FROM feature_cards
UNION ALL
SELECT 'team_members', COUNT(*) FROM team_members
UNION ALL
SELECT 'job_postings', COUNT(*) FROM job_postings
UNION ALL
SELECT 'company_info', COUNT(*) FROM company_info
UNION ALL
SELECT 'page_content', COUNT(*) FROM page_content;
"

echo ""

# Step 6: Generate connection string
echo "🔗 Step 6: Next steps..."

echo ""
echo "✅ Migration completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo "1. Update your application's DATABASE_URL:"
echo "   DATABASE_URL=\"$AWS_RDS_URL\""
echo ""
echo "2. Update environment files:"
echo "   - .env"
echo "   - .env.production"
echo "   - cpanel-deployment/.env"
echo ""
echo "3. Test your application:"
echo "   - Run: npm run dev (locally)"
echo "   - Test database connectivity"
echo "   - Verify all features work"
echo ""
echo "4. Deploy to production:"
echo "   - Update production environment variables"
echo "   - Deploy your application"
echo ""
echo "📁 Generated files:"
echo "   - $SCHEMA_DUMP (schema backup)"
echo "   - $DATA_DUMP (data backup)"
echo "   - $FULL_DUMP (complete backup)"
echo ""
echo "🗑️  Cleanup (optional):"
echo "   rm $SCHEMA_DUMP $DATA_DUMP $FULL_DUMP"
echo ""
echo "🎉 Your Interlock AI database is now running on AWS RDS PostgreSQL!"
