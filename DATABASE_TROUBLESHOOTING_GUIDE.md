# 🔍 Database Connectivity Troubleshooting Guide

## 📦 Updated Deployment Package

**File:** `interlock-ai-cpanel-deployment-fixed.zip`
**Size:** 21.7 MB
**Created:** May 24, 2025 at 11:53 AM

### ✅ What's Fixed in This Package:

1. **Enhanced Environment Loading** - Server.js now manually loads .env files for cPanel compatibility
2. **Improved Database Diagnostics** - Enhanced API endpoint with detailed troubleshooting info
3. **Debug Scripts Included** - Two debugging scripts to help identify issues
4. **Better Error Handling** - More detailed error messages and troubleshooting steps

---

## 🚨 Most Common Database Issues & Solutions

### **1. Environment Variables Not Loaded**

**Symptoms:**
- "DATABASE_URL not found" errors
- Connection timeouts
- Authentication failures

**Solutions:**
1. **Verify .env file exists** in your cPanel File Manager
2. **Copy content from .env.template to .env**
3. **Check file permissions** - ensure readable by Node.js app
4. **Restart your Node.js app** after creating .env file

### **2. cPanel Node.js Environment Issues**

**Symptoms:**
- App starts but database connections fail
- Environment variables not recognized

**Solutions:**
1. **Use cPanel Node.js Selector** to install dependencies
2. **Set Node.js version to 18.x or higher**
3. **Restart the application** after any changes
4. **Check application logs** in cPanel for specific errors

### **3. Database Connection String Issues**

**Your Database URL:**
```
postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
```

**Common Issues:**
- Missing `?sslmode=require` parameter
- Incorrect username/password
- Network connectivity issues

---

## 🛠️ Step-by-Step Debugging Process

### **Step 1: Upload and Extract**
1. Upload `interlock-ai-cpanel-deployment-fixed.zip` to cPanel
2. Extract to your domain's public folder
3. Delete the zip file after extraction

### **Step 2: Create Environment File**
1. Copy `.env.template` to `.env`
2. Verify the DATABASE_URL is exactly:
   ```
   DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"
   ```
3. Update other variables as needed

### **Step 3: Run Debug Script**
1. SSH into your server or use cPanel Terminal
2. Navigate to your application directory
3. Run: `node debug-production-db.js`
4. Review the output for specific issues

### **Step 4: Test Database Connection**
1. Visit: `https://yourdomain.com/api/admin/database-diagnostics`
2. Check the JSON response for connection status
3. Look for specific error messages

### **Step 5: Check Application Logs**
1. In cPanel Node.js Selector, check application logs
2. Look for database connection errors
3. Restart the application if needed

---

## 🔧 Debug Scripts Included

### **1. debug-production-db.js**
- Comprehensive database connectivity test
- Environment variable validation
- Network connectivity checks
- Prisma client testing

**Usage:** `node debug-production-db.js`

### **2. cpanel-env-fix.js**
- Manual environment variable loader
- Can be imported into other files
- Handles multiple .env file locations

**Usage:** `node cpanel-env-fix.js` or `require('./cpanel-env-fix.js')`

---

## 🌐 API Endpoints for Testing

### **Database Diagnostics**
- **URL:** `/api/admin/database-diagnostics`
- **Method:** GET
- **Purpose:** Test database connectivity and get detailed diagnostics

### **Response Example:**
```json
{
  "timestamp": "2025-05-24T16:53:00.000Z",
  "database": {
    "provider": "postgresql",
    "url": "configured",
    "urlMasked": "***********************************************************************************************"
  },
  "connection": {
    "status": "connected",
    "error": null,
    "latency": 245
  },
  "troubleshooting": {
    "envFileExists": true,
    "envFileReadable": true,
    "databaseUrlFormat": "valid"
  }
}
```

---

## 🚨 Common Error Messages & Solutions

### **"ENOTFOUND" Error**
- **Cause:** DNS resolution failed
- **Solution:** Check internet connectivity, verify hostname

### **"ECONNREFUSED" Error**
- **Cause:** Connection refused by database server
- **Solution:** Check port number, verify database is running

### **"Authentication Failed" Error**
- **Cause:** Invalid credentials
- **Solution:** Verify username/password in DATABASE_URL

### **"SSL Connection Error"**
- **Cause:** SSL configuration issue
- **Solution:** Ensure `?sslmode=require` is in DATABASE_URL

---

## 📞 Next Steps if Issues Persist

1. **Check with your hosting provider** about outbound connections
2. **Verify Neon database status** at neon.tech
3. **Test connection from a different server** to isolate the issue
4. **Contact support** with the debug script output

---

## 🔐 Security Notes

- Never expose your DATABASE_URL in logs or error messages
- The debug scripts mask sensitive information
- Always use environment variables for credentials
- Regularly rotate database passwords

---

**Need Help?** Run the debug scripts and check the API diagnostics endpoint for detailed information about your specific issue.
