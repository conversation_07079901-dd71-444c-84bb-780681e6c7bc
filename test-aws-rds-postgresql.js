#!/usr/bin/env node

/**
 * AWS RDS PostgreSQL Connection Test
 * Tests connectivity to your AWS RDS PostgreSQL instance
 */

console.log('🔍 AWS RDS PostgreSQL Connection Test');
console.log('=====================================');
console.log('Target: interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com:5432');
console.log('Date:', new Date().toISOString());
console.log('');

// Test 1: Basic Network Connectivity
async function testNetworkConnectivity() {
  console.log('1. 🌐 Network Connectivity Test:');

  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);

  try {
    // Test DNS resolution
    console.log('   Testing DNS resolution...');
    const dnsResult = await execAsync('nslookup interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com');
    console.log('   ✅ DNS resolution successful');

    // Test port connectivity
    console.log('   Testing port 5432 connectivity...');
    try {
      await execAsync('timeout 10 bash -c "</dev/tcp/interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com/5432"');
      console.log('   ✅ Port 5432 is accessible');
      return true;
    } catch (portError) {
      console.log('   ❌ Port 5432 is not accessible');
      console.log('   Error:', portError.message);
      return false;
    }
  } catch (error) {
    console.log('   ❌ DNS resolution failed');
    console.log('   Error:', error.message);
    return false;
  }
}

// Test 2: PostgreSQL Connection Test
async function testPostgreSQLConnection() {
  console.log('\n2. 🐘 PostgreSQL Connection Test:');

  try {
    // Try to load pg module
    const { Client } = require('pg');
    console.log('   ✅ PostgreSQL client library available');

    // Test connection with actual credentials
    const client = new Client({
      host: 'interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com',
      port: 5432,
      database: 'postgres',
      user: 'lv2srf',
      password: 'Ilv2srf2$$$$',
      connectionTimeoutMillis: 10000,
      ssl: {
        rejectUnauthorized: false
      }
    });

    console.log('   Testing connection with actual credentials...');

    try {
      await client.connect();
      console.log('   ✅ Connection successful!');

      // Test a simple query
      const result = await client.query('SELECT version()');
      console.log('   ✅ Query test successful');
      console.log('   PostgreSQL version:', result.rows[0].version.split(' ')[0]);

      await client.end();
      return true;
    } catch (error) {
      if (error.message.includes('password authentication failed') ||
          error.message.includes('role') ||
          error.message.includes('authentication')) {
        console.log('   ❌ Authentication failed - check credentials');
        console.log('   Error:', error.message);
        return false;
      } else if (error.message.includes('timeout') ||
                 error.message.includes('ECONNREFUSED') ||
                 error.message.includes('ENOTFOUND')) {
        console.log('   ❌ Connection failed - network issue');
        console.log('   Error:', error.message);
        return false;
      } else {
        console.log('   ❌ Unexpected error:', error.message);
        return false;
      }
    }
  } catch (moduleError) {
    console.log('   ⚠️  PostgreSQL client library not available');
    console.log('   Install with: npm install pg');
    return null;
  }
}

// Test 3: Server Information
async function getServerInfo() {
  console.log('\n3. 🖥️  Server Information:');

  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);

  try {
    const ip = await execAsync('curl -s ifconfig.me');
    console.log('   Your server IP:', ip.stdout.trim());

    try {
      const location = await execAsync(`curl -s ipinfo.io/${ip.stdout.trim()}`);
      const locationData = JSON.parse(location.stdout);
      console.log('   Server location:', `${locationData.city}, ${locationData.region}, ${locationData.country}`);
    } catch (locError) {
      console.log('   Could not determine server location');
    }
  } catch (error) {
    console.log('   Could not determine server IP');
  }
}

// Main test function
async function runTests() {
  try {
    await getServerInfo();

    const networkOk = await testNetworkConnectivity();
    const pgResult = await testPostgreSQLConnection();

    console.log('\n📋 Test Results Summary:');
    console.log('========================');

    if (networkOk && pgResult === true) {
      console.log('✅ SUCCESS: Your server can connect to AWS RDS PostgreSQL!');
      console.log('');
      console.log('Next steps:');
      console.log('1. Update your DATABASE_URL with actual RDS credentials');
      console.log('2. Test with your application');
      console.log('3. Migrate your data from Neon to AWS RDS');
    } else if (networkOk && pgResult === null) {
      console.log('⚠️  PARTIAL: Network connection works, but need PostgreSQL client');
      console.log('');
      console.log('Next steps:');
      console.log('1. Install PostgreSQL client: npm install pg');
      console.log('2. Re-run this test');
    } else {
      console.log('❌ FAILED: Cannot connect to AWS RDS PostgreSQL');
      console.log('');
      console.log('Troubleshooting:');
      console.log('1. Check AWS RDS Security Groups');
      console.log('2. Verify RDS instance is running');
      console.log('3. Check your hosting provider firewall');
      console.log('4. Verify the RDS endpoint is correct');
    }

    console.log('');
    console.log('AWS RDS Security Group Requirements:');
    console.log('- Type: PostgreSQL');
    console.log('- Protocol: TCP');
    console.log('- Port: 5432');
    console.log(`- Source: ${await getCurrentIP()}/32 (your server IP)`);

  } catch (error) {
    console.error('Test failed with error:', error.message);
  }
}

async function getCurrentIP() {
  try {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    const result = await execAsync('curl -s ifconfig.me');
    return result.stdout.trim();
  } catch {
    return 'YOUR_SERVER_IP';
  }
}

// Run the tests
runTests();
