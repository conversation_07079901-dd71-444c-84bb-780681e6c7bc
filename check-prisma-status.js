#!/usr/bin/env node

/**
 * Prisma Client Status Check Script
 * 
 * This script checks the status of the Prisma client installation
 * and provides detailed information about the environment.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Prisma Client Status Check 🔍');
console.log('===============================\n');

// Check Node.js version
console.log('📊 Node.js Environment:');
try {
  const nodeVersion = execSync('node --version').toString().trim();
  console.log(`   Node.js Version: ${nodeVersion}`);
} catch (error) {
  console.log('   Node.js Version: Unknown');
}

try {
  const npmVersion = execSync('npm --version').toString().trim();
  console.log(`   npm Version: ${npmVersion}`);
} catch (error) {
  console.log('   npm Version: Unknown');
}

// Check package.json
console.log('\n📦 Package Information:');
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`   Application: ${packageJson.name} v${packageJson.version}`);
  
  // Check for Prisma dependencies
  const hasPrismaClient = packageJson.dependencies && packageJson.dependencies['@prisma/client'];
  const hasPrismaDev = packageJson.devDependencies && packageJson.devDependencies['prisma'];
  
  console.log(`   @prisma/client: ${hasPrismaClient || 'Not found in dependencies'}`);
  console.log(`   prisma: ${hasPrismaDev || (packageJson.dependencies && packageJson.dependencies['prisma']) || 'Not found'}`);
  
  // Check for postinstall script
  const hasPostinstall = packageJson.scripts && packageJson.scripts.postinstall;
  console.log(`   postinstall script: ${hasPostinstall || 'Not found'}`);
  
  if (hasPostinstall && hasPostinstall.includes('prisma generate')) {
    console.log('   ✅ postinstall script includes prisma generate');
  } else if (hasPostinstall) {
    console.log('   ⚠️ postinstall script does not include prisma generate');
  }
} else {
  console.log('   ❌ package.json not found');
}

// Check Prisma schema
console.log('\n📝 Prisma Schema:');
if (fs.existsSync('prisma/schema.prisma')) {
  console.log('   ✅ schema.prisma found');
  
  // Read schema to check datasource
  const schema = fs.readFileSync('prisma/schema.prisma', 'utf8');
  const datasourceMatch = schema.match(/datasource\s+\w+\s*{[^}]*provider\s*=\s*"([^"]+)"/);
  if (datasourceMatch) {
    console.log(`   Database provider: ${datasourceMatch[1]}`);
  }
  
  // Check for env usage
  if (schema.includes('env(')) {
    console.log('   ✅ Using environment variables for connection');
  } else {
    console.log('   ⚠️ Not using environment variables for connection');
  }
} else {
  console.log('   ❌ schema.prisma not found');
}

// Check .env file
console.log('\n🔐 Environment Variables:');
if (fs.existsSync('.env')) {
  console.log('   ✅ .env file found');
  
  // Check for DATABASE_URL without exposing credentials
  const env = fs.readFileSync('.env', 'utf8');
  if (env.includes('DATABASE_URL')) {
    console.log('   ✅ DATABASE_URL found in .env');
    
    // Check database type without exposing credentials
    if (env.includes('postgresql://')) {
      console.log('   Database type: PostgreSQL');
    } else if (env.includes('mysql://')) {
      console.log('   Database type: MySQL');
    } else if (env.includes('sqlite:')) {
      console.log('   Database type: SQLite');
    } else if (env.includes('sqlserver:')) {
      console.log('   Database type: SQL Server');
    } else {
      console.log('   Database type: Unknown');
    }
  } else {
    console.log('   ❌ DATABASE_URL not found in .env');
  }
  
  // Check for Prisma specific environment variables
  if (env.includes('PRISMA_CLI_BINARY_TARGETS')) {
    console.log('   ✅ PRISMA_CLI_BINARY_TARGETS found');
  }
  
  if (env.includes('PRISMA_ENGINES_MIRROR')) {
    console.log('   ✅ PRISMA_ENGINES_MIRROR found');
  }
} else {
  console.log('   ❌ .env file not found');
}

// Check Prisma client installation
console.log('\n🔧 Prisma Client Installation:');
if (fs.existsSync('node_modules/@prisma/client')) {
  console.log('   ✅ @prisma/client installed');
  
  // Check for generated client
  if (fs.existsSync('node_modules/.prisma/client')) {
    console.log('   ✅ Prisma client generated');
    
    // Check for query engine
    const engineFiles = fs.readdirSync('node_modules/.prisma/client')
      .filter(file => file.includes('query-engine') || file.includes('query_engine'));
    
    if (engineFiles.length > 0) {
      console.log(`   ✅ Query engine found: ${engineFiles.join(', ')}`);
    } else {
      console.log('   ❌ Query engine not found');
    }
  } else {
    console.log('   ❌ Prisma client not generated');
  }
} else {
  console.log('   ❌ @prisma/client not installed');
}

// Check for .npmrc
console.log('\n📄 npm Configuration:');
if (fs.existsSync('.npmrc')) {
  console.log('   ✅ .npmrc file found');
  
  const npmrc = fs.readFileSync('.npmrc', 'utf8');
  if (npmrc.includes('legacy-peer-deps=true')) {
    console.log('   ✅ legacy-peer-deps=true found in .npmrc');
  } else {
    console.log('   ⚠️ legacy-peer-deps=true not found in .npmrc');
  }
} else {
  console.log('   ⚠️ .npmrc file not found');
}

// Summary and recommendations
console.log('\n📋 Summary:');

// Check if Prisma client is properly installed and generated
const isPrismaClientInstalled = fs.existsSync('node_modules/@prisma/client');
const isPrismaClientGenerated = fs.existsSync('node_modules/.prisma/client');
const hasSchemaFile = fs.existsSync('prisma/schema.prisma');
const hasEnvFile = fs.existsSync('.env');

if (isPrismaClientInstalled && isPrismaClientGenerated) {
  console.log('   ✅ Prisma client is properly installed and generated');
} else if (isPrismaClientInstalled && !isPrismaClientGenerated) {
  console.log('   ⚠️ Prisma client is installed but not generated');
  console.log('   Run: npx prisma generate');
} else if (!isPrismaClientInstalled) {
  console.log('   ❌ Prisma client is not installed');
  console.log('   Run: npm install @prisma/client');
}

if (!hasSchemaFile) {
  console.log('   ❌ schema.prisma file is missing');
  console.log('   Make sure your schema file is in the prisma directory');
}

if (!hasEnvFile) {
  console.log('   ❌ .env file is missing');
  console.log('   Create a .env file with your DATABASE_URL');
}

console.log('\n🔧 Recommended Actions:');

if (!isPrismaClientInstalled || !isPrismaClientGenerated || !hasSchemaFile || !hasEnvFile) {
  console.log('   Run the fix script: node server-fix-prisma.js');
} else {
  console.log('   ✅ No issues detected with Prisma client installation');
  console.log('   If you are still experiencing issues, try running: node server-fix-prisma.js');
}

console.log('\n🔄 Remember to restart your Node.js application in cPanel after making changes');
