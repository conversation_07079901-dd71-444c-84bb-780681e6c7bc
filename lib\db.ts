// This is a placeholder for the actual database connection
// In a real implementation, you would use <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, or another ORM

export interface ResearchArea {
  id: number
  title: string
  summary: string
  lead: string
  lastUpdated: string
  status: "published" | "in-progress" | "archived"
  category: string
}

export interface FeatureCard {
  id: number
  title: string
  description: string
  icon: string
  order: number
  image?: string
}

// New content types for comprehensive management
export interface PageContent {
  id: number
  pageId: string
  sectionId: string
  title: string
  content: string
  image?: string
  order: number
  isActive: boolean
  lastUpdated: string
}

export interface TeamMember {
  id: number
  name: string
  position: string
  bio: string
  image?: string
  initials: string
  order: number
  isActive: boolean
}

export interface JobPosting {
  id: number
  title: string
  department: string
  location: string
  type: string
  description: string
  requirements: string[]
  isActive: boolean
  postedDate: string
}

export interface CompanyInfo {
  id: number
  section: string
  title: string
  content: string
  image?: string
  order: number
  lastUpdated: string
}

// Mock database for research areas
let researchAreas: ResearchArea[] = [
  {
    id: 1,
    title: "Neural Associative Modeling",
    summary:
      "Creating AI systems that form associations between concepts similar to human cognitive processes, enabling more intuitive learning and reasoning.",
    lead: "<PERSON><PERSON> <PERSON>",
    lastUpdated: "2024-04-15",
    status: "in-progress",
    category: "cognitive",
  },
  {
    id: 2,
    title: "Memory Graph Prioritization",
    summary:
      "Developing algorithms that prioritize information based on relevance, recency, and emotional significance, similar to human memory systems.",
    lead: "Dr. Marcus Chen",
    lastUpdated: "2024-03-28",
    status: "published",
    category: "memory",
  },
  {
    id: 3,
    title: "Contextual Understanding Frameworks",
    summary:
      "Building systems that comprehend nuanced contexts and adapt responses accordingly, improving human-AI interactions.",
    lead: "Dr. Sophia Rodriguez",
    lastUpdated: "2024-04-02",
    status: "published",
    category: "cognitive",
  },
  {
    id: 4,
    title: "Emotional Intelligence Integration",
    summary:
      "Incorporating emotional intelligence into AI decision-making processes to enhance empathy and social awareness in automated systems.",
    lead: "Dr. James Wilson",
    lastUpdated: "2024-03-10",
    status: "in-progress",
    category: "emotional",
  },
  {
    id: 5,
    title: "Adaptive Learning Pathways",
    summary:
      "Creating systems that adjust learning strategies based on performance and engagement, similar to human metacognitive processes.",
    lead: "Dr. Aisha Patel",
    lastUpdated: "2024-04-08",
    status: "published",
    category: "learning",
  },
  {
    id: 6,
    title: "Intuitive Decision Making",
    summary:
      "Modeling human intuition and heuristics to develop AI systems that can make rapid, effective decisions with incomplete information.",
    lead: "Dr. Thomas Nakamura",
    lastUpdated: "2024-02-20",
    status: "archived",
    category: "decision",
  },
]

// Mock database for homepage feature cards
let featureCards: FeatureCard[] = [
  {
    id: 1,
    title: "Neural Associative Modeling",
    description: "Creating AI systems that form associations between concepts similar to human cognitive processes.",
    icon: "🧠",
    order: 1,
    image: "/placeholder.svg?height=400&width=600",
  },
  {
    id: 2,
    title: "Memory Graph Prioritization",
    description: "Developing algorithms that prioritize information based on relevance and emotional significance.",
    icon: "🔄",
    order: 2,
    image: "/placeholder.svg?height=400&width=600",
  },
  {
    id: 3,
    title: "Contextual Understanding",
    description: "Building systems that comprehend nuanced contexts and adapt responses accordingly.",
    icon: "🔍",
    order: 3,
    image: "/placeholder.svg?height=400&width=600",
  },
]

// Mock database for team members
let teamMembers: TeamMember[] = [
  {
    id: 1,
    name: "Dr. Alex Johnson",
    position: "CEO & Co-Founder",
    bio: "Leading expert in cognitive AI with 15+ years of experience in neuroscience and machine learning.",
    initials: "AJ",
    order: 1,
    isActive: true,
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: 2,
    name: "Dr. Maria Lopez",
    position: "CTO & Co-Founder",
    bio: "Former Google AI researcher specializing in neural network architectures and human-computer interaction.",
    initials: "ML",
    order: 2,
    isActive: true,
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: 3,
    name: "Dr. David Chen",
    position: "Head of Research",
    bio: "PhD in Cognitive Psychology with expertise in memory systems and associative learning algorithms.",
    initials: "DC",
    order: 3,
    isActive: true,
    image: "/placeholder.svg?height=300&width=300",
  },
]

// Mock database for job postings
let jobPostings: JobPosting[] = [
  {
    id: 1,
    title: "Senior AI Researcher",
    department: "Research",
    location: "San Francisco, CA",
    type: "Full-time",
    description: "Join our research team to develop cutting-edge human-inspired AI models focusing on neural associative modeling and contextual understanding.",
    requirements: [
      "Ph.D. in Computer Science, Cognitive Science, or related field",
      "5+ years of experience in AI/ML research",
      "Strong publication record in top-tier conferences",
      "Experience with neural network architectures and deep learning frameworks",
    ],
    isActive: true,
    postedDate: "2024-01-15",
  },
  {
    id: 2,
    title: "Machine Learning Engineer",
    department: "Engineering",
    location: "Remote",
    type: "Full-time",
    description: "Build and deploy scalable AI systems that implement our research breakthroughs in production environments.",
    requirements: [
      "Master's degree in Computer Science or related field",
      "3+ years of experience in ML engineering",
      "Proficiency in Python, TensorFlow/PyTorch",
      "Experience with cloud platforms (AWS, GCP, Azure)",
    ],
    isActive: true,
    postedDate: "2024-01-10",
  },
]

// Mock database for company information
let companyInfo: CompanyInfo[] = [
  {
    id: 1,
    section: "mission",
    title: "Our Mission",
    content: "At Interlock, we're on a mission to create artificial intelligence that thinks more like humans do. We believe that by modeling AI systems after human cognitive processes, we can develop technology that's more intuitive, adaptable, and aligned with human values and needs.",
    order: 1,
    lastUpdated: "2024-01-01",
  },
  {
    id: 2,
    section: "story",
    title: "Our Story",
    content: "Interlock was founded in 2020 by a team of researchers who saw the limitations of conventional AI approaches. While traditional AI excelled at pattern recognition and statistical analysis, it often failed to capture the nuanced, contextual, and associative nature of human thought.",
    order: 2,
    lastUpdated: "2024-01-01",
  },
  {
    id: 3,
    section: "values",
    title: "Our Values",
    content: "We believe in transparency, ethical AI development, and the importance of human-centered design. Our work is guided by the principle that AI should augment human capabilities rather than replace them.",
    order: 3,
    lastUpdated: "2024-01-01",
  },
]

// Mock database for page content
let pageContent: PageContent[] = [
  {
    id: 1,
    pageId: "homepage",
    sectionId: "hero",
    title: "Artificial Human-Inspired Learning",
    content: "Pioneering the next generation of AI through human-inspired cognitive models and neural associative frameworks.",
    order: 1,
    isActive: true,
    lastUpdated: "2024-01-01",
    image: "/placeholder.svg?height=600&width=800",
  },
  {
    id: 2,
    pageId: "homepage",
    sectionId: "about",
    title: "Redefining AI Through Human Cognition",
    content: "At Interlock LLC, we're not just building artificial intelligence—we're creating systems that learn, adapt, and reason with human-inspired cognitive frameworks.",
    order: 2,
    isActive: true,
    lastUpdated: "2024-01-01",
    image: "/placeholder.svg?height=600&width=800",
  },
]

// Research Area CRUD operations
export async function getResearchAreas(): Promise<ResearchArea[]> {
  return [...researchAreas]
}

export async function getResearchAreaById(id: number): Promise<ResearchArea | undefined> {
  return researchAreas.find((area) => area.id === id)
}

export async function getResearchAreasByFilter(filter: string): Promise<ResearchArea[]> {
  if (filter === "all") return [...researchAreas]
  return researchAreas.filter((item) => item.category === filter || item.status === filter)
}

export async function addResearchArea(data: Omit<ResearchArea, "id">): Promise<ResearchArea> {
  const newId = Math.max(...researchAreas.map((r) => r.id), 0) + 1
  const newResearch = { ...data, id: newId }
  researchAreas.push(newResearch)
  return newResearch
}

export async function updateResearchArea(id: number, data: Partial<ResearchArea>): Promise<ResearchArea | null> {
  const index = researchAreas.findIndex((area) => area.id === id)
  if (index === -1) return null

  const updatedArea = { ...researchAreas[index], ...data }
  researchAreas[index] = updatedArea
  return updatedArea
}

export async function deleteResearchArea(id: number): Promise<boolean> {
  const initialLength = researchAreas.length
  researchAreas = researchAreas.filter((area) => area.id !== id)
  return initialLength > researchAreas.length
}

// Feature Card CRUD operations
export async function getFeatureCards(): Promise<FeatureCard[]> {
  return [...featureCards].sort((a, b) => a.order - b.order)
}

export async function getFeatureCardById(id: number): Promise<FeatureCard | undefined> {
  return featureCards.find((card) => card.id === id)
}

export async function addFeatureCard(data: Omit<FeatureCard, "id">): Promise<FeatureCard> {
  const newId = Math.max(...featureCards.map((c) => c.id), 0) + 1
  const newCard = { ...data, id: newId }
  featureCards.push(newCard)
  return newCard
}

export async function updateFeatureCard(id: number, data: Partial<FeatureCard>): Promise<FeatureCard | null> {
  const index = featureCards.findIndex((card) => card.id === id)
  if (index === -1) return null

  const updatedCard = { ...featureCards[index], ...data }
  featureCards[index] = updatedCard
  return updatedCard
}

export async function deleteFeatureCard(id: number): Promise<boolean> {
  const initialLength = featureCards.length
  featureCards = featureCards.filter((card) => card.id !== id)
  return initialLength > featureCards.length
}

export async function reorderFeatureCards(orderedIds: number[]): Promise<FeatureCard[]> {
  // Update the order of each card based on its position in the orderedIds array
  orderedIds.forEach((id, index) => {
    const card = featureCards.find((c) => c.id === id)
    if (card) {
      card.order = index + 1
    }
  })

  return [...featureCards].sort((a, b) => a.order - b.order)
}

// Team Members CRUD operations
export async function getTeamMembers(): Promise<TeamMember[]> {
  return [...teamMembers].filter(member => member.isActive).sort((a, b) => a.order - b.order)
}

export async function getTeamMemberById(id: number): Promise<TeamMember | null> {
  return teamMembers.find((member) => member.id === id) || null
}

export async function addTeamMember(memberData: Omit<TeamMember, "id">): Promise<TeamMember> {
  const newId = Math.max(...teamMembers.map((m) => m.id), 0) + 1
  const newMember: TeamMember = {
    id: newId,
    ...memberData,
  }
  teamMembers.push(newMember)
  return newMember
}

export async function updateTeamMember(id: number, updates: Partial<Omit<TeamMember, "id">>): Promise<boolean> {
  const index = teamMembers.findIndex((member) => member.id === id)
  if (index === -1) return false

  teamMembers[index] = { ...teamMembers[index], ...updates }
  return true
}

export async function deleteTeamMember(id: number): Promise<boolean> {
  const index = teamMembers.findIndex((member) => member.id === id)
  if (index === -1) return false

  teamMembers[index].isActive = false
  return true
}

// Job Postings CRUD operations
export async function getJobPostings(): Promise<JobPosting[]> {
  return [...jobPostings].filter(job => job.isActive)
}

export async function getJobPostingById(id: number): Promise<JobPosting | null> {
  return jobPostings.find((job) => job.id === id) || null
}

export async function addJobPosting(jobData: Omit<JobPosting, "id">): Promise<JobPosting> {
  const newId = Math.max(...jobPostings.map((j) => j.id), 0) + 1
  const newJob: JobPosting = {
    id: newId,
    ...jobData,
  }
  jobPostings.push(newJob)
  return newJob
}

export async function updateJobPosting(id: number, updates: Partial<Omit<JobPosting, "id">>): Promise<boolean> {
  const index = jobPostings.findIndex((job) => job.id === id)
  if (index === -1) return false

  jobPostings[index] = { ...jobPostings[index], ...updates }
  return true
}

export async function deleteJobPosting(id: number): Promise<boolean> {
  const index = jobPostings.findIndex((job) => job.id === id)
  if (index === -1) return false

  jobPostings[index].isActive = false
  return true
}

// Company Info CRUD operations
export async function getCompanyInfo(): Promise<CompanyInfo[]> {
  return [...companyInfo].sort((a, b) => a.order - b.order)
}

export async function getCompanyInfoById(id: number): Promise<CompanyInfo | null> {
  return companyInfo.find((info) => info.id === id) || null
}

export async function addCompanyInfo(infoData: Omit<CompanyInfo, "id">): Promise<CompanyInfo> {
  const newId = Math.max(...companyInfo.map((i) => i.id), 0) + 1
  const newInfo: CompanyInfo = {
    id: newId,
    ...infoData,
    lastUpdated: new Date().toISOString().split("T")[0],
  }
  companyInfo.push(newInfo)
  return newInfo
}

export async function updateCompanyInfo(id: number, updates: Partial<Omit<CompanyInfo, "id">>): Promise<boolean> {
  const index = companyInfo.findIndex((info) => info.id === id)
  if (index === -1) return false

  companyInfo[index] = {
    ...companyInfo[index],
    ...updates,
    lastUpdated: new Date().toISOString().split("T")[0],
  }
  return true
}

export async function deleteCompanyInfo(id: number): Promise<boolean> {
  const index = companyInfo.findIndex((info) => info.id === id)
  if (index === -1) return false

  companyInfo.splice(index, 1)
  return true
}

// Page Content CRUD operations
export async function getPageContent(pageId?: string): Promise<PageContent[]> {
  let content = [...pageContent].filter(content => content.isActive)
  if (pageId) {
    content = content.filter(content => content.pageId === pageId)
  }
  return content.sort((a, b) => a.order - b.order)
}

export async function getPageContentById(id: number): Promise<PageContent | null> {
  return pageContent.find((content) => content.id === id) || null
}

export async function addPageContent(contentData: Omit<PageContent, "id">): Promise<PageContent> {
  const newId = Math.max(...pageContent.map((c) => c.id), 0) + 1
  const newContent: PageContent = {
    id: newId,
    ...contentData,
    lastUpdated: new Date().toISOString().split("T")[0],
  }
  pageContent.push(newContent)
  return newContent
}

export async function updatePageContent(id: number, updates: Partial<Omit<PageContent, "id">>): Promise<boolean> {
  const index = pageContent.findIndex((content) => content.id === id)
  if (index === -1) return false

  pageContent[index] = {
    ...pageContent[index],
    ...updates,
    lastUpdated: new Date().toISOString().split("T")[0],
  }
  return true
}

export async function deletePageContent(id: number): Promise<boolean> {
  const index = pageContent.findIndex((content) => content.id === id)
  if (index === -1) return false

  pageContent[index].isActive = false
  return true
}
