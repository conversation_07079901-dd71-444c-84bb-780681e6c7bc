#!/usr/bin/env node

/**
 * Prisma Database Connection Test Script
 * 
 * This script tests the connection to the database using Prisma.
 * It will help identify if there are any issues with the database connection.
 */

// Load environment variables from .env file
require('dotenv').config();

// Import PrismaClient
const { PrismaClient } = require('@prisma/client');

// Create a new instance of PrismaClient
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});

async function main() {
  console.log('🔍 Testing Prisma Database Connection 🔍');
  console.log('======================================\n');
  
  console.log('📊 Environment Information:');
  console.log(`   Node.js Version: ${process.version}`);
  console.log(`   Platform: ${process.platform}`);
  console.log(`   Architecture: ${process.arch}`);
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Check DATABASE_URL without exposing credentials
  if (process.env.DATABASE_URL) {
    console.log('\n🔐 Database Connection:');
    
    // Extract database type and host from connection string without exposing credentials
    const url = new URL(process.env.DATABASE_URL);
    console.log(`   Protocol: ${url.protocol.replace(':', '')}`);
    console.log(`   Host: ${url.hostname}`);
    console.log(`   Port: ${url.port}`);
    console.log(`   Database: ${url.pathname.replace('/', '')}`);
    
    // Test connection
    console.log('\n🔌 Testing connection to database...');
    
    try {
      // Connect to the database
      await prisma.$connect();
      console.log('   ✅ Successfully connected to the database!');
      
      // Get database version
      let dbVersion;
      try {
        if (url.protocol.includes('postgres')) {
          const result = await prisma.$queryRaw`SELECT version();`;
          dbVersion = result[0].version;
        } else if (url.protocol.includes('mysql')) {
          const result = await prisma.$queryRaw`SELECT version() as version;`;
          dbVersion = result[0].version;
        } else if (url.protocol.includes('sqlite')) {
          const result = await prisma.$queryRaw`SELECT sqlite_version() as version;`;
          dbVersion = result[0].version;
        } else if (url.protocol.includes('sqlserver')) {
          const result = await prisma.$queryRaw`SELECT @@VERSION as version;`;
          dbVersion = result[0].version;
        }
        
        if (dbVersion) {
          console.log(`   Database Version: ${dbVersion}`);
        }
      } catch (versionError) {
        console.log('   ⚠️ Could not retrieve database version');
      }
      
      // Test a simple query
      console.log('\n🔍 Testing a simple query...');
      
      // Get all models from the Prisma client
      const models = Object.keys(prisma).filter(key => !key.startsWith('$') && !key.startsWith('_'));
      
      if (models.length > 0) {
        console.log(`   Available models: ${models.join(', ')}`);
        
        // Try to query the first model
        const modelName = models[0];
        console.log(`   Querying the '${modelName}' model...`);
        
        try {
          const count = await prisma[modelName].count();
          console.log(`   ✅ Query successful! Found ${count} records in the '${modelName}' table.`);
        } catch (queryError) {
          console.log(`   ❌ Error querying the '${modelName}' model:`);
          console.log(`   ${queryError.message}`);
        }
      } else {
        console.log('   ⚠️ No models found in the Prisma client');
      }
      
    } catch (error) {
      console.log('   ❌ Failed to connect to the database:');
      console.log(`   ${error.message}`);
      
      // Provide more specific error messages based on the error
      if (error.message.includes('connect ECONNREFUSED')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Check if the database server is running');
        console.log('   2. Verify the hostname and port in your DATABASE_URL');
        console.log('   3. Check if there are any firewall rules blocking the connection');
      } else if (error.message.includes('authentication failed')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Verify your database username and password');
        console.log('   2. Check if the user has the necessary permissions');
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Verify the database name in your DATABASE_URL');
        console.log('   2. Create the database if it does not exist');
      } else if (error.message.includes('SSL')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Check if your database requires SSL');
        console.log('   2. Add ?sslmode=require to your DATABASE_URL if needed');
      }
    } finally {
      // Disconnect from the database
      await prisma.$disconnect();
    }
  } else {
    console.log('\n❌ DATABASE_URL not found in environment variables');
    console.log('   Make sure you have a .env file with a DATABASE_URL variable');
  }
  
  console.log('\n📋 Summary:');
  if (process.env.DATABASE_URL) {
    console.log('   1. DATABASE_URL is set in environment variables');
  } else {
    console.log('   1. ❌ DATABASE_URL is not set in environment variables');
  }
  
  console.log('\n🔧 Next Steps:');
  console.log('   1. If you encountered any errors, check the troubleshooting section');
  console.log('   2. Make sure your database is accessible from this server');
  console.log('   3. Verify your database credentials');
  console.log('   4. Run the server-fix-prisma.js script if needed');
}

// Run the main function
main()
  .catch(e => {
    console.error('Unexpected error:', e);
    process.exit(1);
  });
