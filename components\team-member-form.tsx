"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { ImageUpload } from "@/components/image-upload"
import { addTeamMember, updateTeamMember, type TeamMember } from "@/lib/api-client"

interface TeamMemberFormProps {
  initialData?: TeamMember
  mode?: "add" | "edit"
  onSuccess?: () => void
}

export function TeamMemberForm({ initialData, mode = "add", onSuccess }: TeamMemberFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    position: initialData?.position || "",
    bio: initialData?.bio || "",
    image: initialData?.image || "",
    initials: initialData?.initials || "",
    order_index: initialData?.order_index || 1,
    is_active: initialData?.is_active ?? true,
  })
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      if (mode === "edit" && initialData) {
        const success = await updateTeamMember(initialData.id, formData)
        if (success) {
          toast({
            title: "Team member updated",
            description: "The team member has been updated successfully",
          })
          onSuccess?.()
        } else {
          throw new Error("Failed to update team member")
        }
      } else {
        await addTeamMember(formData)
        toast({
          title: "Team member added",
          description: "The team member has been added successfully",
        })
        onSuccess?.()
        // Reset form for add mode
        setFormData({
          name: "",
          position: "",
          bio: "",
          image: "",
          initials: "",
          order_index: 1,
          is_active: true,
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode} team member`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Auto-generate initials when name changes
  const handleNameChange = (name: string) => {
    updateField("name", name)
    if (name) {
      const initials = name
        .split(" ")
        .map(word => word.charAt(0).toUpperCase())
        .join("")
        .slice(0, 2)
      updateField("initials", initials)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">Full Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleNameChange(e.target.value)}
            placeholder="Dr. Alex Johnson"
            required
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="position">Position *</Label>
          <Input
            id="position"
            value={formData.position}
            onChange={(e) => updateField("position", e.target.value)}
            placeholder="CEO & Co-Founder"
            required
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="initials">Initials</Label>
          <Input
            id="initials"
            value={formData.initials}
            onChange={(e) => updateField("initials", e.target.value.toUpperCase())}
            placeholder="AJ"
            maxLength={3}
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
          <p className="text-xs text-gray-400">Used as fallback if no image is provided</p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="order_index">Display Order</Label>
          <Input
            id="order_index"
            type="number"
            min="1"
            value={formData.order_index}
            onChange={(e) => updateField("order_index", parseInt(e.target.value) || 1)}
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="bio">Biography *</Label>
        <Textarea
          id="bio"
          value={formData.bio}
          onChange={(e) => updateField("bio", e.target.value)}
          placeholder="Leading expert in cognitive AI with 15+ years of experience..."
          required
          rows={4}
          className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
      </div>

      <ImageUpload
        label="Profile Image (Optional)"
        value={formData.image}
        onChange={(value) => updateField("image", value)}
        placeholder="Upload a professional headshot (optional - will show initials if empty)"
      />

      <div className="flex items-center space-x-2">
        <Switch
          id="is_active"
          checked={formData.is_active}
          onCheckedChange={(checked) => updateField("is_active", checked)}
        />
        <Label htmlFor="is_active">Active (visible on website)</Label>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {isSubmitting ? "Saving..." : mode === "edit" ? "Update Team Member" : "Add Team Member"}
        </Button>
      </div>
    </form>
  )
}
