#!/bin/bash

echo "========================================"
echo "  Prisma Client Fix Script"
echo "========================================"
echo

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  echo "❌ Error: package.json not found in current directory"
  echo "Please run this script from your application root directory"
  exit 1
fi

# Check if prisma schema exists
if [ ! -f "prisma/schema.prisma" ]; then
  echo "❌ Error: prisma/schema.prisma not found"
  echo "Please make sure your Prisma schema file exists"
  exit 1
fi

echo "🔍 Checking for Prisma installation..."
if ! command -v npx &> /dev/null; then
  echo "❌ Error: npx command not found"
  echo "Please make sure Node.js is installed correctly"
  exit 1
fi

echo "🧹 Cleaning up any existing Prisma client..."
rm -rf node_modules/.prisma
rm -rf node_modules/@prisma/client

echo "📦 Reinstalling Prisma client..."
npm install @prisma/client

echo "🔧 Generating Prisma client..."
npx prisma generate

if [ $? -eq 0 ]; then
  echo "✅ Prisma client generated successfully!"
else
  echo "❌ Error generating Prisma client"
  echo "Trying with additional options..."
  
  echo "Creating .npmrc with legacy-peer-deps..."
  echo "legacy-peer-deps=true" > .npmrc
  
  echo "Setting Prisma binary targets..."
  export PRISMA_CLI_BINARY_TARGETS="linux-openssl-1.1.x"
  export PRISMA_ENGINES_MIRROR="https://binaries.prisma.sh"
  
  echo "Trying again with binary targets set..."
  npx prisma generate
  
  if [ $? -eq 0 ]; then
    echo "✅ Prisma client generated successfully with binary targets!"
  else
    echo "❌ Error generating Prisma client even with binary targets"
    echo "Please check your database connection and Prisma schema"
    exit 1
  fi
fi

echo "🔄 Restarting application..."
echo "Note: You'll need to restart your Node.js application in cPanel"
echo "Go to cPanel > Setup Node.js App > Select your app > Click 'Restart'"

echo
echo "========================================"
echo "  Prisma Client Fix Complete!"
echo "========================================"
echo
echo "If you still encounter issues, please check:"
echo "1. Your database connection string in .env"
echo "2. That your database is accessible from this server"
echo "3. That your Prisma schema matches your database structure"
echo
echo "For more help, see PRISMA_TROUBLESHOOTING.md"
