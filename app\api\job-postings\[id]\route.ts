import { NextRequest, NextResponse } from 'next/server'
import { getJobPostingById, updateJobPosting, deleteJobPosting } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const jobPosting = await getJobPostingById(id)
    if (!jobPosting) {
      return NextResponse.json(
        { error: 'Job posting not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(jobPosting)
  } catch (error) {
    console.error('Error fetching job posting:', error)
    return NextResponse.json(
      { error: 'Failed to fetch job posting' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { title, department, location, type, description, requirements, is_active } = body

    const success = await updateJobPosting(id, {
      title,
      department,
      location,
      type,
      description,
      requirements,
      is_active,
    })

    if (!success) {
      return NextResponse.json(
        { error: 'Job posting not found or update failed' },
        { status: 404 }
      )
    }

    // Fetch the updated job posting to return it
    const updatedJobPosting = await getJobPostingById(id)
    return NextResponse.json(updatedJobPosting)
  } catch (error) {
    console.error('Error updating job posting:', error)
    return NextResponse.json(
      { error: 'Failed to update job posting' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const success = await deleteJobPosting(id)
    if (!success) {
      return NextResponse.json(
        { error: 'Job posting not found or delete failed' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Job posting deleted successfully' })
  } catch (error) {
    console.error('Error deleting job posting:', error)
    return NextResponse.json(
      { error: 'Failed to delete job posting' },
      { status: 500 }
    )
  }
}
