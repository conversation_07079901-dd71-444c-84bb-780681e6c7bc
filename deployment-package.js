const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// Create deployment package for cPanel
async function createDeploymentPackage() {
  console.log('🚀 Creating production deployment package for cPanel...');

  const output = fs.createWriteStream('interlock-ai-production.zip');
  const archive = archiver('zip', {
    zlib: { level: 9 } // Maximum compression
  });

  output.on('close', function() {
    console.log(`✅ Deployment package created: ${archive.pointer()} total bytes`);
    console.log('📦 File: interlock-ai-production.zip');
    console.log('\n🎯 cPanel Deployment Instructions:');
    console.log('1. Upload interlock-ai-production.zip to your cPanel File Manager');
    console.log('2. Extract the zip file in your public_html directory');
    console.log('3. Ensure Node.js is enabled in cPanel (if available)');
    console.log('4. Set up environment variables in cPanel');
    console.log('5. Your site should be live!');
  });

  archive.on('error', function(err) {
    throw err;
  });

  archive.pipe(output);

  // Add ALL source files for complete deployment
  console.log('📁 Adding Next.js build files...');
  archive.directory('.next/', '.next/');

  // Add public assets
  console.log('🖼️ Adding public assets...');
  archive.directory('public/', 'public/');

  // Add all source code
  console.log('💻 Adding source code...');
  archive.directory('app/', 'app/');
  archive.directory('components/', 'components/');
  archive.directory('lib/', 'lib/');
  archive.directory('hooks/', 'hooks/');

  // Add configuration files
  console.log('⚙️ Adding configuration files...');
  archive.file('package.json', { name: 'package.json' });
  archive.file('package-lock.json', { name: 'package-lock.json' });
  archive.file('next.config.js', { name: 'next.config.js' });
  archive.file('tailwind.config.ts', { name: 'tailwind.config.ts' });
  archive.file('tsconfig.json', { name: 'tsconfig.json' });
  archive.file('components.json', { name: 'components.json' });
  archive.file('postcss.config.mjs', { name: 'postcss.config.mjs' });

  // Add environment files
  if (fs.existsSync('.env.local')) {
    archive.file('.env.local', { name: '.env.local' });
  }
  if (fs.existsSync('.env.example')) {
    archive.file('.env.example', { name: '.env.example' });
  }
  if (fs.existsSync('.env')) {
    archive.file('.env', { name: '.env' });
  }

  // Add documentation
  console.log('📚 Adding documentation...');
  if (fs.existsSync('README.md')) {
    archive.file('README.md', { name: 'README.md' });
  }
  if (fs.existsSync('FINAL_DEPLOYMENT_STATUS.md')) {
    archive.file('FINAL_DEPLOYMENT_STATUS.md', { name: 'FINAL_DEPLOYMENT_STATUS.md' });
  }

  // Add deployment guides
  if (fs.existsSync('CPANEL_DEPLOYMENT_GUIDE.md')) {
    archive.file('CPANEL_DEPLOYMENT_GUIDE.md', { name: 'CPANEL_DEPLOYMENT_GUIDE.md' });
  }
  if (fs.existsSync('DEPLOYMENT_CHECKLIST.md')) {
    archive.file('DEPLOYMENT_CHECKLIST.md', { name: 'DEPLOYMENT_CHECKLIST.md' });
  }

  // Add deployment-specific files
  console.log('📋 Adding deployment files...');

  // Create .htaccess for Apache servers
  const htaccess = `# Next.js Static Export Configuration
RewriteEngine On

# Handle client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.html [L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache static assets
<FilesMatch "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
  ExpiresActive On
  ExpiresDefault "access plus 1 year"
</FilesMatch>

# Compress files
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>`;

  archive.append(htaccess, { name: '.htaccess' });

  // Create deployment README
  const deploymentReadme = `# Interlock AI - Production Deployment

## 🚀 cPanel Deployment Guide

### Prerequisites
- cPanel hosting account with Node.js support (recommended)
- File Manager access
- Domain configured

### Deployment Steps

1. **Upload Files**
   - Upload interlock-ai-production.zip to your cPanel File Manager
   - Navigate to public_html directory
   - Extract the zip file

2. **Environment Configuration**
   - Copy .env.example to .env
   - Configure your environment variables:
     \`\`\`
     NEXT_PUBLIC_SITE_URL=https://yourdomain.com
     DATABASE_URL=your_database_connection_string
     \`\`\`

3. **Node.js Setup (if available)**
   - Enable Node.js in cPanel (version 18.x or higher)
   - Install dependencies: \`npm install\`
   - Build the project: \`npm run build\`
   - Start the application: \`npm start\`
   - Set startup file to: \`server.js\` or \`npm start\`

4. **Static Hosting Alternative**
   - If Node.js is not available, use the pre-built files
   - All pages are pre-rendered in .next/static/
   - Works with any web server (Apache, Nginx, etc.)
   - No server-side processing required

### File Structure
\`\`\`
public_html/
├── .next/           # Next.js build output
├── public/          # Static assets
├── .htaccess        # Apache configuration
├── package.json     # Dependencies
└── README.md        # This file
\`\`\`

### Troubleshooting

**404 Errors on Routes:**
- Ensure .htaccess is in place
- Check if mod_rewrite is enabled

**Assets Not Loading:**
- Verify public/ directory is accessible
- Check file permissions (644 for files, 755 for directories)

**Performance Issues:**
- Enable gzip compression in cPanel
- Configure CDN if available
- Optimize images

### Support
For deployment issues, check:
1. cPanel error logs
2. Browser developer console
3. Network tab for failed requests

### Security
- Keep dependencies updated
- Use HTTPS
- Configure security headers (included in .htaccess)
- Regular backups

## 🎉 Your Interlock AI site should now be live!
`;

  archive.append(deploymentReadme, { name: 'README.md' });

  // Finalize the archive
  console.log('🔄 Finalizing package...');
  await archive.finalize();
}

// Run the packaging
createDeploymentPackage().catch(console.error);
