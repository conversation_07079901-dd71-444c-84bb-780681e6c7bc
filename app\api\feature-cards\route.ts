import { NextRequest, NextResponse } from 'next/server'
import { getFeatureCards, addFeatureCard } from '@/lib/database'
import { getImageOrPlaceholder } from '@/lib/placeholders'

export async function GET() {
  try {
    const featureCards = await getFeatureCards()
    return NextResponse.json(featureCards)
  } catch (error) {
    console.error('Error fetching feature cards:', error)
    return NextResponse.json(
      { error: 'Failed to fetch feature cards' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, description, icon, image, order_index } = body

    if (!title || !description || !icon) {
      return NextResponse.json(
        { error: 'Title, description, and icon are required' },
        { status: 400 }
      )
    }

    const newFeatureCard = await addFeatureCard({
      title,
      description,
      icon,
      image: getImageOrPlaceholder(image, 'FEATURE_CARD'),
      order_index: order_index || 1,
    })

    return NextResponse.json(newFeatureCard, { status: 201 })
  } catch (error) {
    console.error('Error creating feature card:', error)
    return NextResponse.json(
      { error: 'Failed to create feature card' },
      { status: 500 }
    )
  }
}
