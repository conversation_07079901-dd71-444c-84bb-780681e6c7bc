"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getJobPostings, type JobPosting } from "@/lib/api-client"
import { LoadingSpinner } from "@/components/loading-spinner"

export default function CareersPage() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadJobPostings() {
      try {
        const jobs = await getJobPostings()
        // Filter to only show active job postings
        const activeJobs = jobs.filter(job => job.is_active)
        setJobPostings(activeJobs)
      } catch (error) {
        console.error("Failed to load job postings:", error)
      } finally {
        setLoading(false)
      }
    }

    loadJobPostings()
  }, [])

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-5xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Careers at Interlock
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Join our team and help build the future of human-inspired artificial intelligence.
        </p>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-12">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Why Work With Us</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Cutting-Edge Research</h3>
                <p className="text-gray-300">
                  Work on the frontier of AI research, developing systems that think more like humans do.
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Interdisciplinary Collaboration</h3>
                <p className="text-gray-300">
                  Collaborate with experts across neuroscience, psychology, computer science, and more.
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Flexible Work Environment</h3>
                <p className="text-gray-300">
                  Remote-friendly policies and flexible schedules to support your best work.
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Competitive Benefits</h3>
                <p className="text-gray-300">
                  Comprehensive health coverage, generous PTO, 401(k) matching, and continuous learning opportunities.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <h2 className="text-3xl font-bold text-white mb-6">Open Positions</h2>

        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner />
          </div>
        ) : jobPostings.length === 0 ? (
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardContent className="p-8 text-center">
              <h3 className="text-xl font-semibold text-white mb-2">No Open Positions</h3>
              <p className="text-gray-300">
                We don't have any open positions at the moment, but we're always looking for talented individuals.
                Feel free to reach out to us with your resume and we'll keep you in mind for future opportunities.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {jobPostings.map((position) => (
              <Card key={position.id} className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      <CardTitle className="text-xl text-cyan-400">{position.title}</CardTitle>
                      <CardDescription className="text-gray-400">
                        {position.department} • {position.location}
                      </CardDescription>
                    </div>
                    <Badge className="self-start md:self-auto mt-2 md:mt-0 bg-purple-500/20 text-purple-300 hover:bg-purple-500/30">
                      {position.type}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 mb-4">{position.description}</p>
                  {position.requirements && position.requirements.length > 0 && (
                    <>
                      <h4 className="text-lg font-semibold text-white mb-2">Requirements</h4>
                      <ul className="list-disc pl-5 space-y-1 text-gray-300">
                        {position.requirements.map((req: string, index: number) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </>
                  )}
                </CardContent>
                <CardFooter>
                  <Button className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700">
                    Apply Now
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </motion.div>
    </div>
  )
}
