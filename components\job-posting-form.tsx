"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { addJobPosting, updateJobPosting, type JobPosting } from "@/lib/api-client"
import { Plus, X } from "lucide-react"

interface JobPostingFormData {
  title: string
  department: string
  location: string
  type: string
  description: string
  requirements: string[]
  is_active: boolean
}

interface JobPostingFormProps {
  onSuccess?: () => void
  initialData?: JobPosting
  mode?: "create" | "edit"
}

export function JobPostingForm({ onSuccess, initialData, mode = "create" }: JobPostingFormProps) {
  const [formData, setFormData] = useState<JobPostingFormData>({
    title: "",
    department: "",
    location: "",
    type: "Full-time",
    description: "",
    requirements: [""],
    is_active: true,
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Initialize form with data if in edit mode
  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title,
        department: initialData.department,
        location: initialData.location,
        type: initialData.type,
        description: initialData.description,
        requirements: initialData.requirements.length > 0 ? initialData.requirements : [""],
        is_active: initialData.is_active,
      })
    }
  }, [initialData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleRequirementChange = (index: number, value: string) => {
    const newRequirements = [...formData.requirements]
    newRequirements[index] = value
    setFormData((prev) => ({ ...prev, requirements: newRequirements }))
  }

  const addRequirement = () => {
    setFormData((prev) => ({ ...prev, requirements: [...prev.requirements, ""] }))
  }

  const removeRequirement = (index: number) => {
    if (formData.requirements.length > 1) {
      const newRequirements = formData.requirements.filter((_, i) => i !== index)
      setFormData((prev) => ({ ...prev, requirements: newRequirements }))
    }
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.title.trim()) {
      errors.title = "Title is required"
    }

    if (!formData.department.trim()) {
      errors.department = "Department is required"
    }

    if (!formData.location.trim()) {
      errors.location = "Location is required"
    }

    if (!formData.description.trim()) {
      errors.description = "Description is required"
    }

    const validRequirements = formData.requirements.filter(req => req.trim())
    if (validRequirements.length === 0) {
      errors.requirements = "At least one requirement is needed"
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (Object.keys(errors).length > 0) {
      toast({
        title: "Validation Error",
        description: Object.values(errors)[0],
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Filter out empty requirements
      const validRequirements = formData.requirements.filter(req => req.trim())

      if (mode === "edit" && initialData) {
        // Update existing job posting
        const success = await updateJobPosting(initialData.id, {
          title: formData.title,
          department: formData.department,
          location: formData.location,
          type: formData.type,
          description: formData.description,
          requirements: validRequirements,
          is_active: formData.is_active,
        })

        if (success) {
          toast({
            title: "Job posting updated",
            description: "The job posting has been updated successfully",
          })
        } else {
          throw new Error("Update failed")
        }
      } else {
        // Create new job posting
        await addJobPosting({
          title: formData.title,
          department: formData.department,
          location: formData.location,
          type: formData.type,
          description: formData.description,
          requirements: validRequirements,
          is_active: formData.is_active,
        })

        toast({
          title: "Job posting added",
          description: "The new job posting has been added successfully",
        })

        // Reset form if creating new
        if (mode === "create") {
          setFormData({
            title: "",
            department: "",
            location: "",
            type: "Full-time",
            description: "",
            requirements: [""],
            is_active: true,
          })
        }
      }

      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode === "edit" ? "update" : "add"} job posting. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 bg-gray-900/50 backdrop-blur-sm border border-purple-500/20 rounded-lg p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="title" className="text-cyan-400">Job Title</Label>
          <Input
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="e.g., Senior AI Researcher"
            className="bg-gray-800 border-gray-700 text-white"
            required
          />
        </div>

        <div>
          <Label htmlFor="department" className="text-cyan-400">Department</Label>
          <Input
            id="department"
            name="department"
            value={formData.department}
            onChange={handleChange}
            placeholder="e.g., Research"
            className="bg-gray-800 border-gray-700 text-white"
            required
          />
        </div>

        <div>
          <Label htmlFor="location" className="text-cyan-400">Location</Label>
          <Input
            id="location"
            name="location"
            value={formData.location}
            onChange={handleChange}
            placeholder="e.g., San Francisco, CA"
            className="bg-gray-800 border-gray-700 text-white"
            required
          />
        </div>

        <div>
          <Label htmlFor="type" className="text-cyan-400">Job Type</Label>
          <select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white"
            required
          >
            <option value="Full-time">Full-time</option>
            <option value="Part-time">Part-time</option>
            <option value="Contract">Contract</option>
            <option value="Internship">Internship</option>
          </select>
        </div>
      </div>

      <div>
        <Label htmlFor="description" className="text-cyan-400">Job Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Describe the role, responsibilities, and what makes this position exciting..."
          className="bg-gray-800 border-gray-700 text-white min-h-[120px]"
          required
        />
      </div>

      <div>
        <Label className="text-cyan-400">Requirements</Label>
        <div className="space-y-2">
          {formData.requirements.map((requirement, index) => (
            <div key={index} className="flex gap-2">
              <Input
                value={requirement}
                onChange={(e) => handleRequirementChange(index, e.target.value)}
                placeholder={`Requirement ${index + 1}`}
                className="bg-gray-800 border-gray-700 text-white"
              />
              {formData.requirements.length > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeRequirement(index)}
                  className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addRequirement}
            className="border-cyan-500 text-cyan-500 hover:bg-cyan-500 hover:text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Requirement
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is_active"
          checked={formData.is_active}
          onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, is_active: checked }))}
        />
        <Label htmlFor="is_active" className="text-cyan-400">Active Job Posting</Label>
      </div>

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full border-2 border-t-transparent border-white animate-spin mr-2"></div>
              Processing...
            </div>
          ) : mode === "edit" ? (
            "Update Job Posting"
          ) : (
            "Add Job Posting"
          )}
        </Button>
      </div>
    </form>
  )
}
