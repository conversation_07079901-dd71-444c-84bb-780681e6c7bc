# cPanel Deployment Guide - AWS RDS Version

## 🎯 Quick Deployment Summary

Your Interlock AI application is ready for production deployment with AWS RDS PostgreSQL integration.

**ZIP File:** `interlock-ai-cpanel-deployment.zip` (21.42 MB)

---

## 🚀 **Step-by-Step Deployment**

### **Step 1: Upload to cPanel**

1. **Log into your cPanel account**
2. **Open File Manager**
3. **Navigate to `public_html`** (or your domain's directory)
4. **Upload `interlock-ai-cpanel-deployment.zip`**
5. **Extract the ZIP file**
6. **Delete the ZIP file** after extraction

### **Step 2: Environment Configuration**

1. **Copy `.env.template` to `.env`**
   ```bash
   # In cPanel File Manager or Terminal
   cp .env.template .env
   ```

2. **Verify the `.env` file contains:**
   ```bash
   # Database Configuration (AWS RDS PostgreSQL)
   DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"
   
   # Admin Credentials (Update these for security)
   ADMIN_EMAIL="<EMAIL>"
   ADMIN_PASSWORD="your-secure-password-here"
   
   # Next.js Configuration
   NEXTAUTH_SECRET="your-nextauth-secret-here"
   NEXTAUTH_URL="https://your-domain.com"
   ```

3. **Update the following values:**
   - `ADMIN_PASSWORD` - Set a secure admin password
   - `NEXTAUTH_SECRET` - Generate a secure secret key
   - `NEXTAUTH_URL` - Set to your actual domain

### **Step 3: cPanel Node.js Setup**

1. **Go to cPanel → Node.js**
2. **Create New Application:**
   - **Node.js Version:** 18.x or higher
   - **Application Mode:** Production
   - **Application Root:** `/public_html` (or your app directory)
   - **Application URL:** Your domain
   - **Startup File:** `server.js`

3. **Click "Create"**

### **Step 4: Install Dependencies**

1. **In cPanel Node.js interface, click "Run NPM Install"**
2. **Or use Terminal:**
   ```bash
   cd /home/<USER>/public_html
   npm install
   ```

### **Step 5: Database Setup**

1. **Generate Prisma Client:**
   ```bash
   npx prisma generate
   ```

2. **Push Database Schema:**
   ```bash
   npx prisma db push
   ```

3. **Seed Database (Optional):**
   ```bash
   npx prisma db seed
   ```

### **Step 6: Start Application**

1. **In cPanel Node.js interface, click "Restart"**
2. **Or use Terminal:**
   ```bash
   npm start
   ```

---

## ✅ **Verification Steps**

### **1. Check Application Status**
- Visit your domain in a browser
- You should see the Interlock AI homepage

### **2. Test Database Connection**
- Visit: `https://yourdomain.com/api/admin/database-diagnostics`
- Should return JSON with connection status

### **3. Test Admin Access**
- Visit: `https://yourdomain.com/admin`
- Login with your admin credentials

---

## 🔧 **AWS RDS Configuration**

### **Database Details:**
- **Host:** `interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com`
- **Port:** `5432`
- **Database:** `postgres`
- **Username:** `lv2srf`
- **Password:** `Ilv2srf2$$$$`

### **Security Group Requirements:**
Ensure your AWS RDS security group allows:
- **Type:** PostgreSQL
- **Port:** 5432
- **Source:** Your hosting server's IP address

---

## 🚨 **Troubleshooting**

### **Common Issues & Solutions:**

#### **1. Database Connection Failed**
```bash
# Test connection manually
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => console.log('✅ Connected'))
  .catch(err => console.error('❌ Failed:', err.message));
"
```

**Solutions:**
- Check AWS RDS security group settings
- Verify your server's IP is whitelisted
- Confirm database credentials are correct

#### **2. Prisma Client Not Generated**
```bash
# Regenerate Prisma client
npx prisma generate --schema=./prisma/schema.prisma
```

#### **3. Application Won't Start**
```bash
# Check Node.js version
node --version  # Should be 18.x or higher

# Check for errors
npm run dev  # Run in development mode to see errors
```

#### **4. Environment Variables Not Loading**
```bash
# Verify .env file exists and has correct content
cat .env

# Check file permissions
chmod 644 .env
```

---

## 📋 **Post-Deployment Checklist**

- [ ] Application loads successfully
- [ ] Database connection works
- [ ] Admin panel accessible
- [ ] All pages render correctly
- [ ] Forms and interactions work
- [ ] SSL certificate configured (if applicable)
- [ ] Domain DNS pointing correctly

---

## 🔒 **Security Recommendations**

### **1. Change Default Passwords**
- Update `ADMIN_PASSWORD` in `.env`
- Use a strong, unique password

### **2. Secure Environment Variables**
- Ensure `.env` file permissions are restrictive
- Never commit `.env` to version control

### **3. SSL Configuration**
- Enable SSL/HTTPS for your domain
- Update `NEXTAUTH_URL` to use `https://`

### **4. Database Security**
- Regularly rotate database passwords
- Monitor AWS RDS access logs
- Use VPC security groups appropriately

---

## 📞 **Support Resources**

### **If You Need Help:**

1. **Check Application Logs:**
   - cPanel → Node.js → View Logs
   - Look for specific error messages

2. **Test Database Connectivity:**
   ```bash
   # Run the connection test script
   node test-aws-rds-connection.js
   ```

3. **Common Commands:**
   ```bash
   # Restart application
   npm run start
   
   # Check application status
   pm2 status  # If using PM2
   
   # View real-time logs
   npm run logs
   ```

---

## 🎉 **Deployment Complete!**

Your Interlock AI application is now running on:
- **Frontend:** cPanel hosting with Node.js
- **Database:** AWS RDS PostgreSQL
- **SSL:** Secure connections enabled

**Your application should now be live and accessible at your domain!**

### **Key Features Available:**
- ✅ Homepage with cyberpunk design
- ✅ Research areas management
- ✅ Team member profiles
- ✅ Job postings
- ✅ Admin CMS panel
- ✅ Responsive design
- ✅ Database-driven content

Enjoy your production Interlock AI website! 🚀
