"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Lock } from "lucide-react"
import { loginUser, type User } from "@/lib/auth"

export default function AdminPage() {
  const [user, setUser] = useState<User | null>(null)
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const loggedInUser = await loginUser(email, password)

      if (loggedInUser) {
        setUser(loggedInUser)
        toast({
          title: "Login successful",
          description: "Welcome to the admin dashboard",
        })
      } else {
        toast({
          title: "Login failed",
          description: "Invalid email or password",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Login error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-20 flex justify-center items-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm shadow-[0_0_30px_rgba(124,58,237,0.2)]">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-purple-500/20 backdrop-blur-sm flex items-center justify-center border border-purple-500/50">
                <Lock className="w-6 h-6 text-purple-400" />
              </div>
              <CardTitle className="text-2xl text-cyan-400">Admin Login</CardTitle>
              <CardDescription className="text-gray-400">Access the content management system</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Use the credentials from environment variables (.env.local)
                  </p>
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
                >
                  {isSubmitting ? "Logging in..." : "Login"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-6xl mx-auto"
      >
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
            Content Management System
          </h1>
          <div className="flex space-x-4">
            <Button
              onClick={() => window.location.href = '/admin/cms'}
              className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
            >
              Open CMS
            </Button>
            <Button
              variant="outline"
              onClick={() => setUser(null)}
              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
            >
              Logout
            </Button>
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-xl text-cyan-400">Admin Dashboard</CardTitle>
            <CardDescription className="text-gray-400">
              Welcome to the admin panel. Full functionality will be available soon.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-300">
                You are successfully logged in as: <span className="text-cyan-400">{user.email}</span>
              </p>
              <p className="text-gray-300">
                Role: <span className="text-purple-400">{user.role}</span>
              </p>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                <h3 className="text-blue-400 font-semibold mb-2">Environment Variables Setup</h3>
                <p className="text-gray-300 text-sm">
                  Admin credentials are now configured via environment variables:
                </p>
                <ul className="text-gray-400 text-sm mt-2 space-y-1">
                  <li>• ADMIN_EMAIL: {process.env.ADMIN_EMAIL || "<EMAIL>"}</li>
                  <li>• ADMIN_PASSWORD: [Protected]</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
