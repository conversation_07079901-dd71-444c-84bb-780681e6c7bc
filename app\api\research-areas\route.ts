import { NextRequest, NextResponse } from 'next/server'
import { getResearchAreas, addResearchArea } from '@/lib/database'

export async function GET() {
  try {
    const researchAreas = await getResearchAreas()
    return NextResponse.json(researchAreas)
  } catch (error) {
    console.error('Error fetching research areas:', error)
    return NextResponse.json(
      { error: 'Failed to fetch research areas' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, summary, lead, category, status } = body

    if (!title || !summary || !lead || !category) {
      return NextResponse.json(
        { error: 'Title, summary, lead, and category are required' },
        { status: 400 }
      )
    }

    const newResearchArea = await addResearchArea({
      title,
      summary,
      lead,
      category,
      status: status || 'published',
    })

    return NextResponse.json(newResearchArea, { status: 201 })
  } catch (error) {
    console.error('Error creating research area:', error)
    return NextResponse.json(
      { error: 'Failed to create research area' },
      { status: 500 }
    )
  }
}
