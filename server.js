#!/usr/bin/env node

/**
 * Production Server for Interlock AI
 * Optimized for cPanel hosting with Node.js
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Load environment variables
require('dotenv').config();

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

console.log('🚀 Starting Interlock AI Production Server...');
console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`Hostname: ${hostname}`);
console.log(`Port: ${port}`);

// Database connection check
if (process.env.DATABASE_URL) {
  console.log('🔗 Database URL configured');
} else {
  console.warn('⚠️  DATABASE_URL not configured');
}

// Create Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal server error');
    }
  })
  .once('error', (err) => {
    console.error('Server error:', err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(`✅ Interlock AI server ready on http://${hostname}:${port}`);
    console.log(`🔗 Database: ${process.env.DATABASE_URL ? 'Connected' : 'Not configured'}`);
    console.log('📱 Application features:');
    console.log('   - Homepage with cyberpunk design');
    console.log('   - Research areas management');
    console.log('   - Team member profiles');
    console.log('   - Job postings system');
    console.log('   - Admin CMS panel');
    console.log('   - AWS RDS PostgreSQL integration');
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
