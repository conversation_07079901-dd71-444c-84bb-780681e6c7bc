"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { ImageUpload } from "@/components/image-upload"
import { addFeatureCard, updateFeatureCard, type FeatureCard } from "@/lib/api-client"

interface FeatureCardFormData {
  title: string
  description: string
  icon: string
  image: string
  order_index: number
}

interface FeatureCardFormProps {
  onSuccess?: () => void
  initialData?: FeatureCard
  mode?: "create" | "edit"
}

export function FeatureCardForm({ onSuccess, initialData, mode = "create" }: FeatureCardFormProps) {
  const [formData, setFormData] = useState<FeatureCardFormData>({
    title: "",
    description: "",
    icon: "",
    image: "",
    order_index: 999, // Will be set properly when saving
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Initialize form with data if in edit mode
  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title,
        description: initialData.description,
        icon: initialData.icon,
        image: initialData.image || "",
        order_index: initialData.order_index,
      })
    }
  }, [initialData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.title.trim()) {
      errors.title = "Title is required"
    }

    if (!formData.description.trim()) {
      errors.description = "Description is required"
    }

    if (!formData.icon.trim()) {
      errors.icon = "Icon is required"
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (Object.keys(errors).length > 0) {
      // Show the first error
      const firstError = Object.values(errors)[0]
      toast({
        title: "Validation Error",
        description: firstError,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      if (mode === "edit" && initialData) {
        // Update existing feature card
        const result = await updateFeatureCard(initialData.id, {
          title: formData.title,
          description: formData.description,
          icon: formData.icon,
          image: formData.image,
          order_index: formData.order_index,
        })

        if (!result) {
          throw new Error("Update failed - no result returned")
        }

        toast({
          title: "Feature card updated",
          description: "The feature card has been updated successfully",
        })
      } else {
        // Create new feature card
        const result = await addFeatureCard({
          title: formData.title,
          description: formData.description,
          icon: formData.icon,
          image: formData.image,
          order_index: formData.order_index,
        })

        if (!result) {
          throw new Error("Creation failed - no result returned")
        }

        toast({
          title: "Feature card added",
          description: "The new feature card has been added successfully",
        })

        // Reset form if creating new
        if (mode === "create") {
          setFormData({
            title: "",
            description: "",
            icon: "",
            image: "",
            order_index: 999,
          })
        }
      }

      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error("Feature card form error:", error)
      toast({
        title: "Error",
        description: `Failed to ${mode === "edit" ? "update" : "add"} feature card. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="e.g. Neural Associative Modeling"
          className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Describe the feature..."
          className="min-h-24 bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="icon">Icon (Emoji)</Label>
        <Input
          id="icon"
          name="icon"
          value={formData.icon}
          onChange={handleChange}
          placeholder="e.g. 🧠"
          className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
        <p className="text-xs text-gray-400 mt-1">Use an emoji as the icon. For example: 🧠, 🔄, 🔍, 💡, 🤖</p>
      </div>

      <ImageUpload
        label="Feature Image (Optional)"
        value={formData.image}
        onChange={(value) => setFormData(prev => ({ ...prev, image: value }))}
        placeholder="Add an image for this feature (optional - will use placeholder if empty)"
      />

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full border-2 border-t-transparent border-white animate-spin mr-2"></div>
              Processing...
            </div>
          ) : mode === "edit" ? (
            "Update Feature Card"
          ) : (
            "Add Feature Card"
          )}
        </Button>
      </div>
    </form>
  )
}
