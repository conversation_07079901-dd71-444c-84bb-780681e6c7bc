#!/usr/bin/env node

/**
 * cPanel Deployment Preparation Script for Next.js App
 * This script prepares your Next.js application for deployment to cPanel hosting
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting cPanel deployment preparation...\n');

// Configuration
const DEPLOYMENT_DIR = 'cpanel-deployment';
const REQUIRED_FILES = [
  'package.json',
  'next.config.mjs',
  'prisma',
  'public',
  'app',
  'components',
  'lib',
  'hooks',
  'styles',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.mjs',
  'components.json'
];

// Step 1: Clean previous deployment directory
console.log('📁 Cleaning previous deployment directory...');
if (fs.existsSync(DEPLOYMENT_DIR)) {
  fs.rmSync(DEPLOYMENT_DIR, { recursive: true, force: true });
}
fs.mkdirSync(DEPLOYMENT_DIR);

// Step 2: Check dependencies
console.log('📦 Checking dependencies...');
if (fs.existsSync('node_modules')) {
  console.log('✅ Dependencies already installed, skipping installation');
} else {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install --legacy-peer-deps', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
}

console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully');
} catch (error) {
  console.log('⚠️  Warning: Prisma client generation failed, will generate on server');
  console.log('   This is common on Windows due to file permissions');
  console.log('   A postinstall script has been added to package.json to ensure Prisma client is generated on the server');
}

console.log('🔨 Checking for existing build...');
if (fs.existsSync('.next')) {
  console.log('✅ Build already exists, skipping build step');
} else {
  console.log('🔨 Building the application...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Step 3: Copy required files to deployment directory
console.log('📋 Copying required files...');

function copyRecursive(src, dest) {
  if (fs.statSync(src).isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    fs.readdirSync(src).forEach(item => {
      copyRecursive(path.join(src, item), path.join(dest, item));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

REQUIRED_FILES.forEach(file => {
  const srcPath = path.join(process.cwd(), file);
  const destPath = path.join(DEPLOYMENT_DIR, file);

  if (fs.existsSync(srcPath)) {
    console.log(`  ✅ Copying ${file}...`);
    copyRecursive(srcPath, destPath);
  } else {
    console.log(`  ⚠️  Warning: ${file} not found, skipping...`);
  }
});

// Step 4: Copy .next build output
console.log('📦 Copying build output (.next)...');
const nextSrc = path.join(process.cwd(), '.next');
const nextDest = path.join(DEPLOYMENT_DIR, '.next');
if (fs.existsSync(nextSrc)) {
  copyRecursive(nextSrc, nextDest);
  console.log('  ✅ Build output copied');
} else {
  console.error('❌ .next directory not found. Make sure the build was successful.');
  process.exit(1);
}

// Step 5: Skip Prisma client copy for CloudLinux compatibility
console.log('📦 Skipping Prisma client copy for CloudLinux compatibility');
console.log('  ⚠️  Prisma client will be generated on server after npm install');

// Step 6: Install production dependencies
console.log('📦 Installing production dependencies...');
const packageJsonPath = path.join(DEPLOYMENT_DIR, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Create a production package.json without prisma generate
const prodPackageJson = {
  ...packageJson,
  scripts: {
    start: "next start",
    postinstall: "echo 'Prisma client already included'"
  }
};

fs.writeFileSync(packageJsonPath, JSON.stringify(prodPackageJson, null, 2));

// Skip node_modules installation for CloudLinux compatibility
console.log('⚠️  Skipping node_modules installation for CloudLinux NodeJS Selector compatibility');
console.log('   Dependencies will be installed by cPanel NodeJS Selector');

// Step 6: Create environment template
console.log('🔧 Creating environment template...');
const envTemplate = `# Environment variables for production deployment
# Copy this to .env in your cPanel file manager and update the values

# Database Configuration (AWS RDS PostgreSQL)
# Migrated from Neon to AWS RDS for better hosting compatibility
DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"

# Admin Credentials (Update these for security)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password-here"

# Next.js Configuration
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="https://your-domain.com"

# Optional: Server Actions Encryption Key
NEXT_SERVER_ACTIONS_ENCRYPTION_KEY="your-encryption-key-here"
`;

fs.writeFileSync(path.join(DEPLOYMENT_DIR, '.env.template'), envTemplate);

// Step 7: Create deployment instructions
console.log('📝 Creating deployment instructions...');
const instructions = `# cPanel CloudLinux Deployment Instructions

## Files prepared in: ${DEPLOYMENT_DIR}/

### 1. Upload Files to cPanel
- Compress the entire '${DEPLOYMENT_DIR}' folder into a ZIP file
- Upload the ZIP file to your cPanel File Manager
- Extract it in your domain's public_html directory (or subdirectory)

### 2. Environment Setup
- Copy .env.template to .env
- Update the environment variables with your actual values
- Ensure DATABASE_URL points to your Neon database

### 3. Node.js Setup in cPanel (CloudLinux Compatible)
- Go to "Node.js" in cPanel
- Create a new Node.js app
- Set Node.js version to 18.x or higher
- Set Application Root to your upload directory
- Set Application URL to your domain
- Set Startup File to: server.js
- **IMPORTANT**: Let cPanel install dependencies automatically
- The system will create a symlinked node_modules directory

### 4. Install Dependencies
- After creating the Node.js app, cPanel will automatically run npm install
- If you need to install additional packages, use the cPanel interface
- Or run: npm install in the cPanel terminal for your app

### 5. Database Setup
- In cPanel terminal or SSH, navigate to your app directory
- Run: npx prisma generate (to generate Prisma client)
- Run: npx prisma db push (to sync database schema)
- Optional: npx prisma db seed (to seed initial data)

### 6. Start the Application
- Click "Restart" in the Node.js interface
- Your app should now be running!

## CloudLinux Specific Notes
- node_modules is managed by cPanel NodeJS Selector
- Do not upload node_modules directory
- Dependencies are installed in a virtual environment
- Use cPanel interface for package management

## Troubleshooting
- Check error logs in cPanel
- Ensure all environment variables are set
- Verify database connection
- Check file permissions
- If Prisma issues: run 'npx prisma generate' in cPanel terminal
`;

fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'DEPLOYMENT_INSTRUCTIONS.md'), instructions);

// Step 8: Create server.js for cPanel
console.log('🖥️  Creating server.js for cPanel...');
const serverJs = `const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(\`> Ready on http://\${hostname}:\${port}\`);
  });
});
`;

fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'server.js'), serverJs);

console.log('\n✅ Deployment preparation complete!');
console.log(`📁 Files ready in: ${DEPLOYMENT_DIR}/`);
console.log('📖 Read DEPLOYMENT_INSTRUCTIONS.md for next steps');
console.log('\n🎉 Ready for cPanel deployment!');
