import { NextRequest, NextResponse } from 'next/server'
import { getFeatureCardById, updateFeatureCard, deleteFeatureCard } from '@/lib/database'
import { getImageOrPlaceholder } from '@/lib/placeholders'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params
    const id = parseInt(idParam)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const featureCard = await getFeatureCardById(id)
    if (!featureCard) {
      return NextResponse.json(
        { error: 'Feature card not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(featureCard)
  } catch (error) {
    console.error('Error fetching feature card:', error)
    return NextResponse.json(
      { error: 'Failed to fetch feature card' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params
    const id = parseInt(idParam)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { title, description, icon, image, order_index } = body

    const updatedFeatureCard = await updateFeatureCard(id, {
      title,
      description,
      icon,
      image: getImageOrPlaceholder(image, 'FEATURE_CARD'),
      order_index,
    })

    if (!updatedFeatureCard) {
      return NextResponse.json(
        { error: 'Feature card not found or update failed' },
        { status: 404 }
      )
    }

    return NextResponse.json(updatedFeatureCard)
  } catch (error) {
    console.error('Error updating feature card:', error)
    return NextResponse.json(
      { error: 'Failed to update feature card' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params
    const id = parseInt(idParam)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const success = await deleteFeatureCard(id)
    if (!success) {
      return NextResponse.json(
        { error: 'Feature card not found or delete failed' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Feature card deleted successfully' })
  } catch (error) {
    console.error('Error deleting feature card:', error)
    return NextResponse.json(
      { error: 'Failed to delete feature card' },
      { status: 500 }
    )
  }
}
