import { NextRequest, NextResponse } from 'next/server'
import { getPageContent, addPageContent } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const pageId = searchParams.get('pageId')

    const pageContent = await getPageContent(pageId || undefined)
    return NextResponse.json(pageContent)
  } catch (error) {
    console.error('Error fetching page content:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page content' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { page_id, section_id, title, content, image, order_index, is_active } = body

    if (!page_id || !section_id || !title || !content) {
      return NextResponse.json(
        { error: 'Page ID, section ID, title, and content are required' },
        { status: 400 }
      )
    }

    // Use placeholder content image if none provided
    const placeholderContentImage = 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop&crop=center'

    const newPageContent = await addPageContent({
      page_id,
      section_id,
      title,
      content,
      image: image && image.trim() ? image : placeholderContentImage,
      order_index: order_index || 1,
      is_active: is_active !== undefined ? is_active : true,
    })

    return NextResponse.json(newPageContent, { status: 201 })
  } catch (error) {
    console.error('Error creating page content:', error)
    return NextResponse.json(
      { error: 'Failed to create page content' },
      { status: 500 }
    )
  }
}
