"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { OptimizedImage } from "@/components/optimized-image"

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          About Interlock
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Pioneering the next generation of AI through human-inspired cognitive models.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Interlock Team"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Our Mission</h2>
            <p className="text-gray-300 mb-4">
              At Interlock, we're on a mission to create artificial intelligence that thinks more like humans do. We
              believe that by modeling AI systems after human cognitive processes, we can develop technology that's more
              intuitive, adaptable, and aligned with human values and needs.
            </p>
            <p className="text-gray-300">
              Our interdisciplinary approach brings together experts in neuroscience, psychology, computer science, and
              philosophy to create AI systems that can form associations, prioritize information, understand context,
              and demonstrate emotional intelligence in ways that mirror human cognition.
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Our Story</h2>
            <p className="text-gray-300 mb-4">
              Interlock was founded in 2020 by a team of researchers who saw the limitations of conventional AI
              approaches. While traditional AI excelled at pattern recognition and statistical analysis, it often failed
              to capture the nuanced, contextual, and associative nature of human thought.
            </p>
            <p className="text-gray-300 mb-4">
              Our founders believed that by more closely modeling AI after human cognitive processes, we could create
              systems that not only performed better but also interacted with humans in more natural and helpful ways.
              This vision led to the creation of Interlock and our focus on human-inspired AI research.
            </p>
            <p className="text-gray-300">
              Today, Interlock is at the forefront of research in neural associative modeling, memory graph
              prioritization, contextual understanding, and emotional intelligence—all aimed at creating AI that thinks
              more like we do.
            </p>
          </CardContent>
        </Card>

        
      </motion.div>
    </div>
  )
}
