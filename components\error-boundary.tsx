"use client"

import { Component, type ErrorInfo, type <PERSON>actNode } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  }

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Uncaught error:", error, errorInfo)
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md border-red-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-xl text-red-400">Something went wrong</CardTitle>
              <CardDescription className="text-gray-400">
                An error occurred while rendering this component
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-300 mb-4">{this.state.error?.message || "Unknown error"}</p>
            </CardContent>
            <CardFooter>
              <Button
                onClick={() => this.setState({ hasError: false })}
                className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
              >
                Try again
              </Button>
            </CardFooter>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}
