-- =====================================================
-- AWS RDS PostgreSQL Data Migration Script
-- Seed data for Interlock AI Database
-- =====================================================

-- Insert Research Areas
INSERT INTO research_areas (title, summary, lead, status, category) VALUES
('Neural Associative Modeling', 'Creating AI systems that form associations between concepts similar to human cognitive processes, enabling more intuitive learning and reasoning.', 'Dr<PERSON>', 'in-progress', 'cognitive'),
('Memory Graph Prioritization', 'Developing algorithms that prioritize information based on relevance, recency, and emotional significance, similar to human memory systems.', 'Dr. <PERSON>', 'published', 'memory'),
('Contextual Understanding Frameworks', 'Building systems that comprehend nuanced contexts and adapt responses accordingly, improving human-AI interactions.', 'Dr<PERSON> <PERSON>', 'published', 'cognitive'),
('Emotional Intelligence Integration', 'Incorporating emotional intelligence into AI decision-making processes to enhance empathy and social awareness in automated systems.', 'Dr<PERSON> <PERSON>', 'in-progress', 'emotional');

-- Insert Feature Cards
INSERT INTO feature_cards (title, description, icon, image, order_index) VALUES
('Neural Associative Modeling', 'Creating AI systems that form associations between concepts similar to human cognitive processes.', '🧠', '/placeholder.svg?height=400&width=600', 1),
('Memory Graph Prioritization', 'Developing algorithms that prioritize information based on relevance and emotional significance.', '🔄', '/placeholder.svg?height=400&width=600', 2),
('Contextual Understanding', 'Building systems that comprehend nuanced contexts and adapt responses accordingly.', '🔍', '/placeholder.svg?height=400&width=600', 3);

-- Insert Team Members
INSERT INTO team_members (name, position, bio, initials, order_index, is_active, image) VALUES
('Dr. Alex Johnson', 'CEO & Co-Founder', 'Leading expert in cognitive AI with 15+ years of experience in neuroscience and machine learning.', 'AJ', 1, true, '/placeholder.svg?height=300&width=300'),
('Dr. Maria Lopez', 'CTO & Co-Founder', 'Former Google AI researcher specializing in neural network architectures and human-computer interaction.', 'ML', 2, true, '/placeholder.svg?height=300&width=300'),
('Dr. David Chen', 'Head of Research', 'PhD in Cognitive Psychology with expertise in memory systems and associative learning algorithms.', 'DC', 3, true, '/placeholder.svg?height=300&width=300');

-- Insert Job Postings
INSERT INTO job_postings (title, department, location, type, description, requirements, is_active) VALUES
('Senior AI Researcher', 'Research', 'San Francisco, CA', 'Full-time', 'Join our research team to develop cutting-edge human-inspired AI models focusing on neural associative modeling and contextual understanding.', ARRAY['Ph.D. in Computer Science, Cognitive Science, or related field', '5+ years of experience in AI/ML research', 'Strong publication record in top-tier conferences', 'Experience with neural network architectures and deep learning frameworks'], true),
('Machine Learning Engineer', 'Engineering', 'Remote', 'Full-time', 'Build and deploy scalable AI systems that implement our research breakthroughs in production environments.', ARRAY['Master''s degree in Computer Science or related field', '3+ years of experience in ML engineering', 'Proficiency in Python, TensorFlow/PyTorch', 'Experience with cloud platforms (AWS, GCP, Azure)'], true);

-- Insert Company Info
INSERT INTO company_info (section, title, content, order_index) VALUES
('mission', 'Our Mission', 'At Interlock, we''re on a mission to create artificial intelligence that thinks more like humans do. We believe that by modeling AI systems after human cognitive processes, we can develop technology that''s more intuitive, adaptable, and aligned with human values and needs.', 1),
('story', 'Our Story', 'Interlock was founded in 2020 by a team of researchers who saw the limitations of conventional AI approaches. While traditional AI excelled at pattern recognition and statistical analysis, it often failed to capture the nuanced, contextual, and associative nature of human thought.', 2),
('values', 'Our Values', 'We believe in transparency, ethical AI development, and the importance of human-centered design. Our work is guided by the principle that AI should augment human capabilities rather than replace them.', 3);

-- Insert Page Content
INSERT INTO page_content (page_id, section_id, title, content, order_index, is_active, image) VALUES
('homepage', 'hero', 'Artificial Human-Inspired Learning', 'Pioneering the next generation of AI through human-inspired cognitive models and neural associative frameworks.', 1, true, '/placeholder.svg?height=600&width=800'),
('homepage', 'about', 'Redefining AI Through Human Cognition', 'At Interlock LLC, we''re not just building artificial intelligence—we''re creating systems that learn, adapt, and reason with human-inspired cognitive frameworks.', 2, true, '/placeholder.svg?height=600&width=800');

-- Insert sample testimonials (if needed)
INSERT INTO testimonials (name, role, text, is_active) VALUES
('Dr. Sarah Mitchell', 'AI Research Director', 'Interlock''s approach to human-inspired AI is revolutionary. Their neural associative models show unprecedented understanding of context and nuance.', true),
('Prof. Michael Zhang', 'Stanford University', 'The work being done at Interlock represents the future of artificial intelligence - systems that truly understand rather than just process.', true);

-- Insert sample site content (if needed for legacy compatibility)
INSERT INTO site_content (key, title, content, updated_by, section, order_index) VALUES
('about_intro', 'About Interlock AI', 'We are pioneering the next generation of artificial intelligence through human-inspired cognitive models.', 1, 'about', 1),
('research_intro', 'Our Research', 'Our research focuses on creating AI systems that learn and reason like humans do.', 1, 'research', 1);

-- =====================================================
-- Data Verification Queries
-- =====================================================

-- Count records in each table
DO $$
DECLARE
    rec RECORD;
    table_count INTEGER;
BEGIN
    RAISE NOTICE 'Data Migration Summary:';
    RAISE NOTICE '=====================';
    
    FOR rec IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
    LOOP
        EXECUTE format('SELECT COUNT(*) FROM %I', rec.table_name) INTO table_count;
        RAISE NOTICE '% records in %', table_count, rec.table_name;
    END LOOP;
    
    RAISE NOTICE '=====================';
    RAISE NOTICE 'Data migration completed successfully!';
END $$;

-- =====================================================
-- Optional: Create admin user (uncomment if needed)
-- =====================================================

-- Uncomment the following lines to create an admin user
-- Remember to change the password!

/*
INSERT INTO users (username, password, email, name, role) VALUES
('admin', '$2b$10$example.hash.here', '<EMAIL>', 'Administrator', 'admin');
*/

-- =====================================================
-- Migration Complete
-- =====================================================
