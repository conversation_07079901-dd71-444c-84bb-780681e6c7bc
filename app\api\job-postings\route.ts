import { NextRequest, NextResponse } from 'next/server'
import { getJobPostings, addJobPosting } from '@/lib/database'

export async function GET() {
  try {
    const jobPostings = await getJobPostings()
    return NextResponse.json(jobPostings)
  } catch (error) {
    console.error('Error fetching job postings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch job postings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, department, location, type, description, requirements, is_active } = body

    if (!title || !department || !location || !type || !description) {
      return NextResponse.json(
        { error: 'Title, department, location, type, and description are required' },
        { status: 400 }
      )
    }

    const newJobPosting = await addJobPosting({
      title,
      department,
      location,
      type,
      description,
      requirements: requirements || [],
      is_active: is_active !== undefined ? is_active : true,
    })

    return NextResponse.json(newJobPosting, { status: 201 })
  } catch (error) {
    console.error('Error creating job posting:', error)
    return NextResponse.json(
      { error: 'Failed to create job posting' },
      { status: 500 }
    )
  }
}
