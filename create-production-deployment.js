#!/usr/bin/env node

/**
 * Create Production Deployment Package for cPanel
 * Includes all source files for npm install on server
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('🚀 Creating Production Deployment Package for cPanel');
console.log('===================================================');

const DEPLOYMENT_NAME = 'interlock-ai-production-deployment';
const ZIP_NAME = `${DEPLOYMENT_NAME}.zip`;

// Remove existing zip file if it exists
if (fs.existsSync(ZIP_NAME)) {
  fs.unlinkSync(ZIP_NAME);
  console.log('🗑️  Removed existing ZIP file');
}

// Create a file to stream archive data to
const output = fs.createWriteStream(ZIP_NAME);
const archive = archiver('zip', {
  zlib: { level: 9 } // Maximum compression
});

// Listen for all archive data to be written
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`\n✅ Production deployment package created successfully!`);
  console.log(`📁 File: ${ZIP_NAME}`);
  console.log(`📊 Size: ${sizeInMB} MB`);
  console.log(`📦 Total bytes: ${archive.pointer()}`);
  console.log('\n🚀 Ready for cPanel deployment!');
  console.log('\nDeployment Instructions:');
  console.log('1. Upload this ZIP file to your cPanel File Manager');
  console.log('2. Extract it in your domain\'s public_html directory');
  console.log('3. Copy .env.template to .env and configure');
  console.log('4. Run: npm install');
  console.log('5. Set up cPanel Node.js app with server.js');
  console.log('6. Run: npx prisma generate && npx prisma db push');
});

// Handle warnings and errors
archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('⚠️  Warning:', err.message);
  } else {
    throw err;
  }
});

archive.on('error', function(err) {
  console.error('❌ Error creating ZIP:', err.message);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

console.log('📁 Adding files to deployment package...');

// Files and directories to include
const filesToInclude = [
  // Core application files
  'app/',
  'components/',
  'lib/',
  'hooks/',
  'prisma/',
  'public/',
  'styles/',
  
  // Configuration files
  'package.json',
  'package-lock.json',
  'next.config.mjs',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.mjs',
  'components.json',
  
  // Environment and deployment files
  '.env.template',
  'server.js'
];

// Files and directories to exclude
const excludePatterns = [
  'node_modules/',
  '.next/',
  '.git/',
  '.env',
  '*.zip',
  'cpanel-deployment/',
  'deployment-packages/',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
];

// Function to check if a file should be excluded
function shouldExclude(filePath) {
  return excludePatterns.some(pattern => {
    if (pattern.endsWith('/')) {
      return filePath.startsWith(pattern) || filePath.includes('/' + pattern);
    }
    if (pattern.startsWith('*.')) {
      return filePath.endsWith(pattern.substring(1));
    }
    return filePath === pattern || filePath.includes('/' + pattern);
  });
}

// Add files to archive
filesToInclude.forEach(item => {
  const fullPath = path.resolve(item);
  
  if (fs.existsSync(fullPath)) {
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      console.log(`   📂 Adding directory: ${item}`);
      archive.directory(fullPath, item, (entryData) => {
        // Exclude unwanted files
        if (shouldExclude(entryData.name)) {
          return false;
        }
        return entryData;
      });
    } else {
      console.log(`   📄 Adding file: ${item}`);
      archive.file(fullPath, { name: item });
    }
  } else {
    console.log(`   ⚠️  File not found: ${item}`);
  }
});

// Create production environment template
console.log('   📝 Creating production .env.template...');
const envTemplate = `# Environment variables for production deployment
# Copy this to .env and update the values

# Database Configuration (AWS RDS PostgreSQL)
# Migrated from Neon to AWS RDS for better hosting compatibility
DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"

# Admin Credentials (CHANGE THESE FOR SECURITY)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password-here"

# Next.js Configuration (UPDATE THESE)
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="https://your-domain.com"
NODE_ENV="production"

# Optional: Server Actions Encryption Key
NEXT_SERVER_ACTIONS_ENCRYPTION_KEY="your-encryption-key-here"

# Prisma configuration for production
PRISMA_SCHEMA_ENGINE_TYPE=binary
PRISMA_QUERY_ENGINE_TYPE=binary
PRISMA_CLI_QUERY_ENGINE_TYPE=binary
`;

archive.append(envTemplate, { name: '.env.template' });

// Create production server.js
console.log('   📝 Creating production server.js...');
const serverJs = `#!/usr/bin/env node

/**
 * Production Server for Interlock AI
 * Optimized for cPanel hosting with Node.js
 */

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Load environment variables
require('dotenv').config();

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

console.log('🚀 Starting Interlock AI Production Server...');
console.log(\`Environment: \${process.env.NODE_ENV || 'development'}\`);
console.log(\`Hostname: \${hostname}\`);
console.log(\`Port: \${port}\`);

// Create Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal server error');
    }
  })
  .once('error', (err) => {
    console.error('Server error:', err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(\`✅ Interlock AI server ready on http://\${hostname}:\${port}\`);
    console.log(\`🔗 Database: \${process.env.DATABASE_URL ? 'Connected' : 'Not configured'}\`);
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received, shutting down gracefully');
  process.exit(0);
});
`;

archive.append(serverJs, { name: 'server.js' });

// Create deployment instructions
console.log('   📝 Creating deployment instructions...');
const deploymentInstructions = `# Interlock AI - Production Deployment Instructions

## 🚀 cPanel Deployment Guide

### Prerequisites
- cPanel hosting account with Node.js support (18.x or higher)
- File Manager access
- Domain configured

### Step 1: Upload and Extract
1. Upload \`${ZIP_NAME}\` to your cPanel File Manager
2. Navigate to your domain's public_html directory
3. Extract the ZIP file
4. Delete the ZIP file after extraction

### Step 2: Environment Configuration
1. Copy \`.env.template\` to \`.env\`:
   \`\`\`bash
   cp .env.template .env
   \`\`\`

2. Edit \`.env\` file and update:
   - \`ADMIN_PASSWORD\` - Set a strong password
   - \`NEXTAUTH_SECRET\` - Generate a secure random string
   - \`NEXTAUTH_URL\` - Set to your actual domain URL

### Step 3: Install Dependencies
Run in your application directory:
\`\`\`bash
npm install
\`\`\`

### Step 4: Set up cPanel Node.js Application
1. Go to cPanel → Node.js
2. Create New Application:
   - **Node.js Version:** 18.x or higher
   - **Application Mode:** Production
   - **Application Root:** /public_html (or your app directory)
   - **Application URL:** Your domain
   - **Startup File:** server.js
3. Click "Create"

### Step 5: Database Setup
\`\`\`bash
# Generate Prisma client
npx prisma generate

# Push database schema
npx prisma db push

# Seed database (optional)
npx prisma db seed
\`\`\`

### Step 6: Start Application
1. In cPanel Node.js interface, click "Restart"
2. Or use terminal: \`npm start\`

## ✅ Verification
- Visit your domain - should show Interlock AI homepage
- Test admin panel: \`https://yourdomain.com/admin\`
- Check database: \`https://yourdomain.com/api/admin/database-diagnostics\`

## 🔧 Troubleshooting
- Check Node.js logs in cPanel
- Verify environment variables are set
- Ensure database connection is working
- Check file permissions

## 📞 Support
Refer to the included documentation files for detailed troubleshooting.

## 🎉 Success!
Your Interlock AI application should now be live!
`;

archive.append(deploymentInstructions, { name: 'DEPLOYMENT_INSTRUCTIONS.md' });

// Create package.json scripts for production
console.log('   📝 Adding production scripts...');

// Finalize the archive
archive.finalize();
