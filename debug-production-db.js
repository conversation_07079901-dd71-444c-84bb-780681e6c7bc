#!/usr/bin/env node

/**
 * Production Database Debug Script
 * Run this on your production server to diagnose database connectivity issues
 */

console.log('🔍 Production Database Connectivity Debugger');
console.log('===========================================\n');

// Check Node.js environment
console.log('📊 Environment Information:');
console.log(`   Node.js Version: ${process.version}`);
console.log(`   Platform: ${process.platform}`);
console.log(`   Architecture: ${process.arch}`);
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}\n`);

// Check if .env file exists
const fs = require('fs');
const path = require('path');

console.log('📁 Environment File Check:');
const envPath = path.join(process.cwd(), '.env');
if (fs.existsSync(envPath)) {
  console.log('   ✅ .env file exists');
  
  // Read .env file and check for DATABASE_URL (without exposing it)
  const envContent = fs.readFileSync(envPath, 'utf8');
  const hasDbUrl = envContent.includes('DATABASE_URL=');
  console.log(`   ${hasDbUrl ? '✅' : '❌'} DATABASE_URL found in .env file`);
  
  if (hasDbUrl) {
    // Extract and validate DATABASE_URL format
    const dbUrlMatch = envContent.match(/DATABASE_URL="?([^"\n]+)"?/);
    if (dbUrlMatch) {
      const dbUrl = dbUrlMatch[1];
      try {
        const url = new URL(dbUrl);
        console.log('   ✅ DATABASE_URL format is valid');
        console.log(`   📍 Host: ${url.hostname}`);
        console.log(`   🔌 Port: ${url.port || 'default'}`);
        console.log(`   🗄️  Database: ${url.pathname.replace('/', '')}`);
        console.log(`   🔐 SSL: ${url.searchParams.get('sslmode') || 'not specified'}`);
      } catch (error) {
        console.log('   ❌ DATABASE_URL format is invalid');
        console.log(`   Error: ${error.message}`);
      }
    }
  }
} else {
  console.log('   ❌ .env file not found');
  console.log('   💡 Create .env file with DATABASE_URL');
}

// Check environment variable
console.log('\n🔐 Environment Variable Check:');
if (process.env.DATABASE_URL) {
  console.log('   ✅ DATABASE_URL is set in environment');
  
  try {
    const url = new URL(process.env.DATABASE_URL);
    console.log('   ✅ DATABASE_URL format is valid');
    console.log(`   📍 Host: ${url.hostname}`);
    console.log(`   🔌 Port: ${url.port || 'default'}`);
    console.log(`   🗄️  Database: ${url.pathname.replace('/', '')}`);
    console.log(`   🔐 SSL: ${url.searchParams.get('sslmode') || 'not specified'}`);
  } catch (error) {
    console.log('   ❌ DATABASE_URL format is invalid');
    console.log(`   Error: ${error.message}`);
  }
} else {
  console.log('   ❌ DATABASE_URL not found in environment variables');
}

// Test basic network connectivity
console.log('\n🌐 Network Connectivity Test:');
const { execSync } = require('child_process');

try {
  // Test if we can resolve the hostname
  if (process.env.DATABASE_URL) {
    const url = new URL(process.env.DATABASE_URL);
    console.log(`   Testing connection to ${url.hostname}...`);
    
    // Try to ping the host (if ping is available)
    try {
      execSync(`ping -c 1 ${url.hostname}`, { stdio: 'pipe', timeout: 5000 });
      console.log('   ✅ Host is reachable');
    } catch (pingError) {
      console.log('   ⚠️  Ping failed (this might be normal for cloud databases)');
    }
  }
} catch (error) {
  console.log('   ❌ Network test failed');
  console.log(`   Error: ${error.message}`);
}

// Test Prisma client
console.log('\n🔧 Prisma Client Test:');
try {
  // Load dotenv to ensure environment variables are loaded
  require('dotenv').config();
  
  const { PrismaClient } = require('@prisma/client');
  console.log('   ✅ Prisma Client imported successfully');
  
  const prisma = new PrismaClient({
    log: ['error'],
  });
  
  console.log('   ✅ Prisma Client instantiated');
  
  // Test connection
  console.log('   🔌 Testing database connection...');
  
  prisma.$connect()
    .then(() => {
      console.log('   ✅ Database connection successful!');
      
      // Test a simple query
      return prisma.$queryRaw`SELECT 1 as test`;
    })
    .then(() => {
      console.log('   ✅ Database query successful!');
      return prisma.$disconnect();
    })
    .then(() => {
      console.log('   ✅ Database disconnected cleanly');
      console.log('\n🎉 All tests passed! Database connectivity is working.');
    })
    .catch((error) => {
      console.log('   ❌ Database connection failed');
      console.log(`   Error: ${error.message}`);
      
      // Provide specific troubleshooting based on error type
      if (error.message.includes('ENOTFOUND')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   - DNS resolution failed');
        console.log('   - Check if the hostname is correct');
        console.log('   - Verify internet connectivity');
      } else if (error.message.includes('ECONNREFUSED')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   - Connection refused by server');
        console.log('   - Check if the port is correct');
        console.log('   - Verify the database server is running');
      } else if (error.message.includes('authentication')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   - Authentication failed');
        console.log('   - Check username and password');
        console.log('   - Verify database credentials');
      } else if (error.message.includes('SSL')) {
        console.log('\n🔧 Troubleshooting:');
        console.log('   - SSL connection issue');
        console.log('   - Add ?sslmode=require to DATABASE_URL');
        console.log('   - Check SSL certificate validity');
      }
      
      prisma.$disconnect();
    });
    
} catch (error) {
  console.log('   ❌ Failed to load Prisma Client');
  console.log(`   Error: ${error.message}`);
  
  if (error.message.includes('Cannot find module')) {
    console.log('\n🔧 Troubleshooting:');
    console.log('   - Prisma Client not installed');
    console.log('   - Run: npm install @prisma/client');
    console.log('   - Run: npx prisma generate');
  }
}

console.log('\n📋 Next Steps:');
console.log('1. Fix any issues identified above');
console.log('2. Ensure your hosting provider allows outbound connections');
console.log('3. Check if your database provider has IP restrictions');
console.log('4. Verify your cPanel Node.js app is configured correctly');
