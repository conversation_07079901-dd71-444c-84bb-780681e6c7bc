"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { addFooterPage, updateFooterPage, type FooterPage } from "@/lib/api-client"

interface FooterPageFormProps {
  initialData?: FooterPage | null
  mode: "create" | "edit"
  onSuccess: () => void
}

export function FooterPageForm({ initialData, mode, onSuccess }: FooterPageFormProps) {
  const [formData, setFormData] = useState({
    category: initialData?.category || 'research' as 'research' | 'company' | 'legal',
    title: initialData?.title || '',
    slug: initialData?.slug || '',
    content: initialData?.content || '',
    meta_description: initialData?.meta_description || '',
    is_published: initialData?.is_published ?? true,
    order_index: initialData?.order_index || 0,
  })
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  // Auto-generate slug from title
  useEffect(() => {
    if (mode === "create" && formData.title && !formData.slug) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
      setFormData(prev => ({ ...prev, slug }))
    }
  }, [formData.title, mode, formData.slug])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (mode === "create") {
        await addFooterPage(formData)
        toast({
          title: "Success",
          description: "Footer page created successfully",
        })
      } else if (initialData) {
        await updateFooterPage(initialData.id, formData)
        toast({
          title: "Success",
          description: "Footer page updated successfully",
        })
      }
      onSuccess()
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode} footer page`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select
            value={formData.category}
            onValueChange={(value) => handleInputChange('category', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="research">Research</SelectItem>
              <SelectItem value="company">Company</SelectItem>
              <SelectItem value="legal">Legal</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="order_index">Order Index</Label>
          <Input
            id="order_index"
            type="number"
            value={formData.order_index}
            onChange={(e) => handleInputChange('order_index', parseInt(e.target.value) || 0)}
            className="bg-gray-800 border-gray-700"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => handleInputChange('title', e.target.value)}
          placeholder="Page title"
          required
          className="bg-gray-800 border-gray-700"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="slug">URL Slug</Label>
        <Input
          id="slug"
          value={formData.slug}
          onChange={(e) => handleInputChange('slug', e.target.value)}
          placeholder="url-slug"
          required
          className="bg-gray-800 border-gray-700"
        />
        <p className="text-xs text-gray-400">
          URL will be: /{formData.category}/{formData.slug}
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="meta_description">Meta Description</Label>
        <Input
          id="meta_description"
          value={formData.meta_description}
          onChange={(e) => handleInputChange('meta_description', e.target.value)}
          placeholder="SEO meta description"
          className="bg-gray-800 border-gray-700"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Content</Label>
        <Textarea
          id="content"
          value={formData.content}
          onChange={(e) => handleInputChange('content', e.target.value)}
          placeholder="Page content (supports Markdown)"
          rows={12}
          required
          className="bg-gray-800 border-gray-700"
        />
        <p className="text-xs text-gray-400">
          Supports Markdown formatting
        </p>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="is_published"
          checked={formData.is_published}
          onCheckedChange={(checked) => handleInputChange('is_published', checked)}
        />
        <Label htmlFor="is_published">Published</Label>
      </div>

      <div className="flex space-x-4">
        <Button
          type="submit"
          disabled={loading}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {loading ? "Saving..." : mode === "create" ? "Create Page" : "Update Page"}
        </Button>
      </div>
    </form>
  )
}
