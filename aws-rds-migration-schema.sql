-- =====================================================
-- AWS RDS PostgreSQL Migration Schema Script
-- Interlock AI Database Migration from Neon to AWS RDS
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop tables if they exist (for clean migration)
DROP TABLE IF EXISTS page_content CASCADE;
DROP TABLE IF EXISTS company_info CASCADE;
DROP TABLE IF EXISTS job_postings CASCADE;
DROP TABLE IF EXISTS team_members CASCADE;
DROP TABLE IF EXISTS feature_cards CASCADE;
DROP TABLE IF EXISTS research_areas CASCADE;
DROP TABLE IF EXISTS wishlists CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS testimonials CASCADE;
DROP TABLE IF EXISTS subscribers CASCADE;
DROP TABLE IF EXISTS site_content CASCADE;
DROP TABLE IF EXISTS sessions CASCADE;
DROP TABLE IF EXISTS reviews CASCADE;
DROP TABLE IF EXISTS news_tickers CASCADE;
DROP TABLE IF EXISTS homepage_settings CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;
DROP TABLE IF EXISTS books_read CASCADE;
DROP TABLE IF EXISTS books CASCADE;
DROP TABLE IF EXISTS authors CASCADE;

-- Create authors table
CREATE TABLE authors (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    bio TEXT NOT NULL,
    genre TEXT NOT NULL,
    image_url TEXT,
    initials TEXT NOT NULL,
    twitter_url TEXT,
    instagram_url TEXT,
    facebook_url TEXT,
    website_url TEXT,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER NOT NULL,
    avg_rating DECIMAL(3,1)
);

-- Create books table
CREATE TABLE books (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    author VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    cover_image VARCHAR(255) NOT NULL,
    isbn VARCHAR(50),
    published TIMESTAMP(6),
    genre VARCHAR(100) NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    is_free BOOLEAN DEFAULT false,
    free_url VARCHAR(255),
    amazon_url VARCHAR(255),
    is_new_release BOOLEAN DEFAULT false,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    formats JSONB
);

-- Create books_read table
CREATE TABLE books_read (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    book_id INTEGER NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create chat_messages table
CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    username VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    emoji_reactions JSONB DEFAULT '{}'
);

-- Create homepage_settings table
CREATE TABLE homepage_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(500),
    content TEXT,
    image_url TEXT,
    cta_text VARCHAR(100),
    cta_link VARCHAR(255),
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER NOT NULL,
    release_date TIMESTAMP(6)
);

-- Create news_tickers table
CREATE TABLE news_tickers (
    id SERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER NOT NULL
);

-- Create reviews table
CREATE TABLE reviews (
    id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    rating DECIMAL(2,1) NOT NULL,
    review TEXT,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    is_approved BOOLEAN DEFAULT false
);

-- Create sessions table
CREATE TABLE sessions (
    sid VARCHAR PRIMARY KEY,
    sess JSONB NOT NULL,
    expire TIMESTAMP(6) NOT NULL
);

-- Create index on sessions expire
CREATE INDEX "IDX_sessions_expire" ON sessions (expire);

-- Create site_content table
CREATE TABLE site_content (
    id SERIAL PRIMARY KEY,
    key VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER NOT NULL,
    image_url TEXT,
    section VARCHAR(50),
    order_index INTEGER DEFAULT 0
);

-- Create subscribers table
CREATE TABLE subscribers (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    name VARCHAR(255),
    preferences JSONB
);

-- Create testimonials table
CREATE TABLE testimonials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(100) NOT NULL,
    text TEXT NOT NULL,
    avatar_url VARCHAR(255),
    is_active BOOLEAN DEFAULT true
);

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'customer',
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create wishlists table
CREATE TABLE wishlists (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    book_id INTEGER NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- Interlock AI Specific Tables
-- =====================================================

-- Create research_areas table
CREATE TABLE research_areas (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    summary TEXT NOT NULL,
    lead TEXT NOT NULL,
    last_updated TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'published',
    category TEXT NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create feature_cards table
CREATE TABLE feature_cards (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    icon TEXT NOT NULL,
    image TEXT,
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create team_members table
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    position TEXT NOT NULL,
    bio TEXT NOT NULL,
    image TEXT,
    initials TEXT NOT NULL,
    order_index INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create job_postings table
CREATE TABLE job_postings (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    department TEXT NOT NULL,
    location TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[] NOT NULL,
    is_active BOOLEAN DEFAULT true,
    posted_date TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create company_info table
CREATE TABLE company_info (
    id SERIAL PRIMARY KEY,
    section TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image TEXT,
    order_index INTEGER DEFAULT 1,
    last_updated TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create page_content table
CREATE TABLE page_content (
    id SERIAL PRIMARY KEY,
    page_id TEXT NOT NULL,
    section_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    image TEXT,
    order_index INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    last_updated TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(page_id, section_id)
);

-- =====================================================
-- Create Indexes for Performance
-- =====================================================

-- Indexes for better query performance
CREATE INDEX idx_books_genre ON books(genre);
CREATE INDEX idx_books_featured ON books(is_featured);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_research_areas_status ON research_areas(status);
CREATE INDEX idx_research_areas_category ON research_areas(category);
CREATE INDEX idx_feature_cards_order ON feature_cards(order_index);
CREATE INDEX idx_team_members_active ON team_members(is_active);
CREATE INDEX idx_team_members_order ON team_members(order_index);
CREATE INDEX idx_job_postings_active ON job_postings(is_active);
CREATE INDEX idx_company_info_section ON company_info(section);
CREATE INDEX idx_page_content_page ON page_content(page_id);
CREATE INDEX idx_page_content_active ON page_content(is_active);

-- =====================================================
-- Set up triggers for updated_at timestamps
-- =====================================================

-- Function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for tables with updated_at
CREATE TRIGGER update_research_areas_updated_at BEFORE UPDATE ON research_areas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_cards_updated_at BEFORE UPDATE ON feature_cards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_members_updated_at BEFORE UPDATE ON team_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_job_postings_updated_at BEFORE UPDATE ON job_postings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_company_info_updated_at BEFORE UPDATE ON company_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_page_content_updated_at BEFORE UPDATE ON page_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Migration Complete
-- =====================================================

-- Display completion message
DO $$
BEGIN
    RAISE NOTICE 'AWS RDS PostgreSQL schema migration completed successfully!';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Run the data migration script';
    RAISE NOTICE '2. Update your application DATABASE_URL';
    RAISE NOTICE '3. Test the application connectivity';
END $$;
