import { NextRequest, NextResponse } from 'next/server'
import { getTeamMembers, addTeamMember } from '@/lib/database'

export async function GET() {
  try {
    const teamMembers = await getTeamMembers()
    return NextResponse.json(teamMembers)
  } catch (error) {
    console.error('Error fetching team members:', error)
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, position, bio, image, initials, order_index, is_active } = body

    if (!name || !position || !bio || !initials) {
      return NextResponse.json(
        { error: 'Name, position, bio, and initials are required' },
        { status: 400 }
      )
    }

    // Use placeholder avatar if no image provided
    const placeholderAvatar = 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'

    const newTeamMember = await addTeamMember({
      name,
      position,
      bio,
      image: image && image.trim() ? image : placeholderAvatar,
      initials,
      order_index: order_index || 1,
      is_active: is_active !== undefined ? is_active : true,
    })

    return NextResponse.json(newTeamMember, { status: 201 })
  } catch (error) {
    console.error('Error creating team member:', error)
    return NextResponse.json(
      { error: 'Failed to create team member' },
      { status: 500 }
    )
  }
}
