"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { addResearchArea, updateResearchArea, type ResearchArea } from "@/lib/api-client"

interface ResearchFormData {
  title: string
  summary: string
  lead: string
  lastUpdated: string
  category: string
  status: string
}

interface ResearchFormProps {
  onSuccess?: () => void
  initialData?: ResearchArea
  mode?: "create" | "edit"
}

export function ResearchForm({ onSuccess, initialData, mode = "create" }: ResearchFormProps) {
  const [formData, setFormData] = useState<ResearchFormData>({
    title: "",
    summary: "",
    lead: "",
    lastUpdated: new Date().toISOString().split("T")[0],
    category: "",
    status: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Initialize form with data if in edit mode
  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title,
        summary: initialData.summary,
        lead: initialData.lead,
        lastUpdated: initialData.lastUpdated,
        category: initialData.category,
        status: initialData.status,
      })
    }
  }, [initialData])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.title.trim()) {
      errors.title = "Title is required"
    }

    if (!formData.summary.trim()) {
      errors.summary = "Summary is required"
    }

    if (!formData.lead.trim()) {
      errors.lead = "Research lead is required"
    }

    if (!formData.category) {
      errors.category = "Category is required"
    }

    if (!formData.status) {
      errors.status = "Status is required"
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (Object.keys(errors).length > 0) {
      // Show the first error
      const firstError = Object.values(errors)[0]
      toast({
        title: "Validation Error",
        description: firstError,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      if (mode === "edit" && initialData) {
        // Update existing research
        await updateResearchArea(initialData.id, {
          title: formData.title,
          summary: formData.summary,
          lead: formData.lead,
          lastUpdated: formData.lastUpdated,
          category: formData.category as any,
          status: formData.status as any,
        })

        toast({
          title: "Research updated",
          description: "The research area has been updated successfully",
        })
      } else {
        // Create new research
        await addResearchArea({
          title: formData.title,
          summary: formData.summary,
          lead: formData.lead,
          lastUpdated: formData.lastUpdated,
          category: formData.category as any,
          status: formData.status as any,
        })

        toast({
          title: "Research added",
          description: "The new research area has been added successfully",
        })

        // Reset form if creating new
        if (mode === "create") {
          setFormData({
            title: "",
            summary: "",
            lead: "",
            lastUpdated: new Date().toISOString().split("T")[0],
            category: "",
            status: "",
          })
        }
      }

      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${mode === "edit" ? "update" : "add"} research. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="title">Research Title</Label>
        <Input
          id="title"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="e.g. Neural Associative Modeling"
          className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="summary">Summary</Label>
        <Textarea
          id="summary"
          name="summary"
          value={formData.summary}
          onChange={handleChange}
          placeholder="Describe the research area..."
          className="min-h-32 bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="lead">Research Lead</Label>
          <Input
            id="lead"
            name="lead"
            value={formData.lead}
            onChange={handleChange}
            placeholder="e.g. Dr. Elena Kowalski"
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastUpdated">Last Updated</Label>
          <Input
            id="lastUpdated"
            name="lastUpdated"
            type="date"
            value={formData.lastUpdated}
            onChange={handleChange}
            className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value) => handleSelectChange("category", value)}>
            <SelectTrigger className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cognitive">Cognitive</SelectItem>
              <SelectItem value="memory">Memory</SelectItem>
              <SelectItem value="emotional">Emotional</SelectItem>
              <SelectItem value="learning">Learning</SelectItem>
              <SelectItem value="decision">Decision</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleSelectChange("status", value)}>
            <SelectTrigger className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="image">Featured Image</Label>
        <Input id="image" type="file" className="bg-gray-800/50 border-purple-500/20 focus:border-purple-500/50" />
      </div>

      <div className="flex justify-end">
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {isSubmitting ? (
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full border-2 border-t-transparent border-white animate-spin mr-2"></div>
              Processing...
            </div>
          ) : mode === "edit" ? (
            "Update Research"
          ) : (
            "Add Research"
          )}
        </Button>
      </div>
    </form>
  )
}
