// Client-side API functions for CMS operations
// These replace the direct database calls to work in the browser

export interface FeatureCard {
  id: number
  title: string
  description: string
  icon: string
  image?: string | null
  order_index: number
  created_at: Date
  updated_at: Date
}

export interface JobPosting {
  id: number
  title: string
  department: string
  location: string
  type: string
  description: string
  requirements: string[]
  is_active: boolean
  posted_date: Date
  created_at: Date
  updated_at: Date
}

export interface ResearchArea {
  id: number
  title: string
  summary: string
  lead: string
  last_updated: Date
  status: string
  category: string
  created_at: Date
}

export interface TeamMember {
  id: number
  name: string
  position: string
  bio: string
  image?: string | null
  initials: string
  order_index: number
  is_active: boolean
  created_at: Date
  updated_at: Date
}

export interface PageContent {
  id: number
  page_id: string
  section_id: string
  title: string
  content: string
  image?: string | null
  order_index: number
  is_active: boolean
  last_updated: Date
  created_at: Date
}

export interface FooterPage {
  id: number
  category: 'research' | 'company' | 'legal'
  title: string
  slug: string
  content: string
  meta_description?: string | null
  is_published: boolean
  order_index: number
  created_at: Date
  updated_at: Date
}

// Feature Cards API
export async function getFeatureCards(): Promise<FeatureCard[]> {
  const response = await fetch('/api/feature-cards')
  if (!response.ok) {
    throw new Error('Failed to fetch feature cards')
  }
  return response.json()
}

export async function addFeatureCard(data: Omit<FeatureCard, "id" | "created_at" | "updated_at">): Promise<FeatureCard> {
  const response = await fetch('/api/feature-cards', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add feature card')
  }
  return response.json()
}

export async function updateFeatureCard(id: number, data: Partial<Omit<FeatureCard, "id" | "created_at" | "updated_at">>): Promise<FeatureCard> {
  const response = await fetch(`/api/feature-cards/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to update feature card')
  }
  return response.json()
}

export async function deleteFeatureCard(id: number): Promise<boolean> {
  const response = await fetch(`/api/feature-cards/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

// Job Postings API
export async function getJobPostings(): Promise<JobPosting[]> {
  const response = await fetch('/api/job-postings')
  if (!response.ok) {
    throw new Error('Failed to fetch job postings')
  }
  return response.json()
}

export async function addJobPosting(data: Omit<JobPosting, "id" | "created_at" | "updated_at" | "posted_date">): Promise<JobPosting> {
  const response = await fetch('/api/job-postings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add job posting')
  }
  return response.json()
}

export async function updateJobPosting(id: number, data: Partial<Omit<JobPosting, "id" | "created_at" | "updated_at" | "posted_date">>): Promise<boolean> {
  const response = await fetch(`/api/job-postings/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.ok
}

export async function deleteJobPosting(id: number): Promise<boolean> {
  const response = await fetch(`/api/job-postings/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

// Research Areas API
export async function getResearchAreas(): Promise<ResearchArea[]> {
  const response = await fetch('/api/research-areas')
  if (!response.ok) {
    throw new Error('Failed to fetch research areas')
  }
  return response.json()
}

export async function addResearchArea(data: Omit<ResearchArea, "id" | "created_at" | "last_updated">): Promise<ResearchArea> {
  const response = await fetch('/api/research-areas', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add research area')
  }
  return response.json()
}

export async function updateResearchArea(id: number, data: Partial<Omit<ResearchArea, "id" | "created_at" | "last_updated">>): Promise<boolean> {
  const response = await fetch(`/api/research-areas/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.ok
}

export async function deleteResearchArea(id: number): Promise<boolean> {
  const response = await fetch(`/api/research-areas/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

// Team Members API
export async function getTeamMembers(): Promise<TeamMember[]> {
  const response = await fetch('/api/team-members')
  if (!response.ok) {
    throw new Error('Failed to fetch team members')
  }
  return response.json()
}

export async function addTeamMember(data: Omit<TeamMember, "id" | "created_at" | "updated_at">): Promise<TeamMember> {
  const response = await fetch('/api/team-members', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add team member')
  }
  return response.json()
}

export async function updateTeamMember(id: number, data: Partial<Omit<TeamMember, "id" | "created_at" | "updated_at">>): Promise<boolean> {
  const response = await fetch(`/api/team-members/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.ok
}

export async function deleteTeamMember(id: number): Promise<boolean> {
  const response = await fetch(`/api/team-members/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

// Page Content API
export async function getPageContent(pageId?: string): Promise<PageContent[]> {
  const url = pageId ? `/api/page-content?pageId=${pageId}` : '/api/page-content'
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error('Failed to fetch page content')
  }
  return response.json()
}

export async function addPageContent(data: Omit<PageContent, "id" | "created_at" | "last_updated">): Promise<PageContent> {
  const response = await fetch('/api/page-content', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add page content')
  }
  return response.json()
}

export async function updatePageContent(id: number, data: Partial<Omit<PageContent, "id" | "created_at" | "last_updated">>): Promise<boolean> {
  const response = await fetch(`/api/page-content/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.ok
}

export async function deletePageContent(id: number): Promise<boolean> {
  const response = await fetch(`/api/page-content/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

// Placeholder functions for compatibility
export async function getCompanyInfo() {
  return []
}

export async function getFeatureCardById(id: number) {
  const response = await fetch(`/api/feature-cards/${id}`)
  if (!response.ok) return null
  return response.json()
}

export async function getJobPostingById(id: number) {
  const response = await fetch(`/api/job-postings/${id}`)
  if (!response.ok) return null
  return response.json()
}

export async function getResearchAreaById(id: number) {
  const response = await fetch(`/api/research-areas/${id}`)
  if (!response.ok) return null
  return response.json()
}

export async function getTeamMemberById(id: number) {
  const response = await fetch(`/api/team-members/${id}`)
  if (!response.ok) return null
  return response.json()
}

export async function getPageContentById(id: number) {
  const response = await fetch(`/api/page-content/${id}`)
  if (!response.ok) return null
  return response.json()
}

// Footer Pages API
export async function getFooterPages(category?: string): Promise<FooterPage[]> {
  const url = category ? `/api/footer-pages?category=${category}` : '/api/footer-pages'
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error('Failed to fetch footer pages')
  }
  return response.json()
}

export async function addFooterPage(data: Omit<FooterPage, "id" | "created_at" | "updated_at">): Promise<FooterPage> {
  const response = await fetch('/api/footer-pages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  if (!response.ok) {
    throw new Error('Failed to add footer page')
  }
  return response.json()
}

export async function updateFooterPage(id: number, data: Partial<Omit<FooterPage, "id" | "created_at" | "updated_at">>): Promise<boolean> {
  const response = await fetch(`/api/footer-pages/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  })
  return response.ok
}

export async function deleteFooterPage(id: number): Promise<boolean> {
  const response = await fetch(`/api/footer-pages/${id}`, {
    method: 'DELETE',
  })
  return response.ok
}

export async function getFooterPageById(id: number) {
  const response = await fetch(`/api/footer-pages/${id}`)
  if (!response.ok) return null
  return response.json()
}
