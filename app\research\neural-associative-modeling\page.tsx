"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { OptimizedImage } from "@/components/optimized-image"

export default function NeuralAssociativeModelingPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Neural Associative Modeling
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Creating AI systems that form associations between concepts similar to human cognitive processes.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Neural Associative Modeling"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Overview</h2>
            <p className="text-gray-300 mb-4">
              Neural Associative Modeling is a groundbreaking approach to artificial intelligence that mimics the human
              brain's ability to form associations between different concepts and ideas. Unlike traditional AI systems
              that rely on explicit programming or statistical patterns, our research focuses on creating networks that
              can dynamically establish connections between related concepts, much like the human mind.
            </p>
            <p className="text-gray-300">
              This approach enables more intuitive learning and reasoning capabilities, allowing AI systems to make
              connections that weren't explicitly programmed. By modeling the associative processes of human cognition,
              we're developing AI that can better understand context, generate more creative solutions, and adapt to new
              situations with greater flexibility.
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Key Research Areas</h2>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Associative Memory Networks</h3>
                  <p className="text-gray-300">
                    Developing neural network architectures that can store and retrieve information based on
                    associations rather than exact matches.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Concept Formation</h3>
                  <p className="text-gray-300">
                    Investigating how AI systems can autonomously form concepts from raw sensory data, similar to human
                    abstraction processes.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Associative Learning Algorithms</h3>
                  <p className="text-gray-300">
                    Creating new learning algorithms that prioritize forming meaningful associations between related
                    data points.
                  </p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Team</h2>
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold mr-4">
                EK
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white"></h3>
                <p className="text-gray-300">Lead Researcher, Neural Associative Modeling</p>
              </div>
            </div>
            <p className="text-gray-300">
              Our team combines expertise in neuroscience, computer science, and cognitive psychology to develop AI
              systems that can form and utilize associations in ways similar to human cognition. We collaborate with
              researchers across disciplines to ensure our models accurately reflect the latest understanding of human
              associative processes.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
