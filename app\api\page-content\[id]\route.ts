import { NextRequest, NextResponse } from 'next/server'
import { getPageContentById, updatePageContent, deletePageContent } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const pageContent = await getPageContentById(id)
    if (!pageContent) {
      return NextResponse.json(
        { error: 'Page content not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(pageContent)
  } catch (error) {
    console.error('Error fetching page content:', error)
    return NextResponse.json(
      { error: 'Failed to fetch page content' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { page_id, section_id, title, content, image, order_index, is_active } = body

    // Use placeholder content image if none provided
    const placeholderContentImage = 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop&crop=center'

    const success = await updatePageContent(id, {
      page_id,
      section_id,
      title,
      content,
      image: image && image.trim() ? image : placeholderContentImage,
      order_index,
      is_active,
    })

    if (!success) {
      return NextResponse.json(
        { error: 'Page content not found or update failed' },
        { status: 404 }
      )
    }

    // Fetch the updated page content to return it
    const updatedPageContent = await getPageContentById(id)
    return NextResponse.json(updatedPageContent)
  } catch (error) {
    console.error('Error updating page content:', error)
    return NextResponse.json(
      { error: 'Failed to update page content' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const success = await deletePageContent(id)
    if (!success) {
      return NextResponse.json(
        { error: 'Page content not found or delete failed' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Page content deleted successfully' })
  } catch (error) {
    console.error('Error deleting page content:', error)
    return NextResponse.json(
      { error: 'Failed to delete page content' },
      { status: 500 }
    )
  }
}
