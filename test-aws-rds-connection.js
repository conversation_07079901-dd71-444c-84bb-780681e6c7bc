#!/usr/bin/env node

/**
 * AWS RDS PostgreSQL Connection Test for Interlock AI
 * Tests the new AWS RDS database connection with actual credentials
 */

console.log('🔍 AWS RDS PostgreSQL Connection Test');
console.log('=====================================');
console.log('Testing connection to: interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com');
console.log('Database: postgres');
console.log('User: lv2srf');
console.log('Date:', new Date().toISOString());
console.log('');

// Load environment variables
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('1. 🔧 Environment Check:');
  
  // Check if DATABASE_URL is set
  if (process.env.DATABASE_URL) {
    console.log('   ✅ DATABASE_URL found in environment');
    
    // Mask the password for display
    const maskedUrl = process.env.DATABASE_URL.replace(/:([^:@]+)@/, ':****@');
    console.log('   📍 URL:', maskedUrl);
  } else {
    console.log('   ⚠️  DATABASE_URL not found in environment');
    console.log('   Using hardcoded connection for testing...');
  }
  
  console.log('');
  
  // Test 2: Network connectivity
  console.log('2. 🌐 Network Connectivity:');
  
  const { exec } = require('child_process');
  const { promisify } = require('util');
  const execAsync = promisify(exec);
  
  try {
    // Test DNS resolution
    console.log('   Testing DNS resolution...');
    await execAsync('nslookup interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com');
    console.log('   ✅ DNS resolution successful');
    
    // Test port connectivity
    console.log('   Testing port 5432 connectivity...');
    try {
      await execAsync('timeout 10 bash -c "</dev/tcp/interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com/5432"');
      console.log('   ✅ Port 5432 is accessible');
    } catch (portError) {
      console.log('   ❌ Port 5432 is not accessible');
      console.log('   This may indicate firewall or security group issues');
      return false;
    }
  } catch (error) {
    console.log('   ❌ Network connectivity test failed');
    console.log('   Error:', error.message);
    return false;
  }
  
  console.log('');
  
  // Test 3: PostgreSQL connection
  console.log('3. 🐘 PostgreSQL Connection Test:');
  
  try {
    const { Client } = require('pg');
    console.log('   ✅ PostgreSQL client library available');
    
    // Use environment variable or fallback to hardcoded connection
    const connectionString = process.env.DATABASE_URL || 
      '*******************************************************************************************************/postgres?sslmode=require';
    
    const client = new Client({
      connectionString: connectionString,
      connectionTimeoutMillis: 15000,
    });
    
    console.log('   Connecting to AWS RDS PostgreSQL...');
    
    await client.connect();
    console.log('   ✅ Database connection successful!');
    
    // Test basic query
    console.log('   Testing basic query...');
    const versionResult = await client.query('SELECT version()');
    console.log('   ✅ Query successful');
    console.log('   PostgreSQL version:', versionResult.rows[0].version.split(' ')[0]);
    
    // Test database info
    console.log('   Getting database information...');
    const dbInfoResult = await client.query('SELECT current_database(), current_user, inet_server_addr(), inet_server_port()');
    const dbInfo = dbInfoResult.rows[0];
    console.log('   📊 Database:', dbInfo.current_database);
    console.log('   👤 User:', dbInfo.current_user);
    console.log('   🖥️  Server:', dbInfo.inet_server_addr || 'localhost');
    console.log('   🔌 Port:', dbInfo.inet_server_port || 5432);
    
    // Test table creation (to verify permissions)
    console.log('   Testing table creation permissions...');
    try {
      await client.query('CREATE TABLE IF NOT EXISTS connection_test (id SERIAL PRIMARY KEY, test_time TIMESTAMP DEFAULT NOW())');
      await client.query('INSERT INTO connection_test DEFAULT VALUES');
      const testResult = await client.query('SELECT COUNT(*) as count FROM connection_test');
      console.log('   ✅ Table operations successful');
      console.log('   📝 Test records:', testResult.rows[0].count);
      
      // Clean up test table
      await client.query('DROP TABLE IF EXISTS connection_test');
      console.log('   🧹 Test table cleaned up');
    } catch (permError) {
      console.log('   ⚠️  Table creation test failed (may be normal for restricted users)');
      console.log('   Error:', permError.message);
    }
    
    await client.end();
    console.log('   🔌 Connection closed');
    
    return true;
    
  } catch (error) {
    console.log('   ❌ PostgreSQL connection failed');
    console.log('   Error:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('   🔍 Issue: Authentication failed - check username/password');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('   🔍 Issue: Database does not exist - check database name');
    } else if (error.message.includes('timeout')) {
      console.log('   🔍 Issue: Connection timeout - check network/firewall');
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('   🔍 Issue: Host not found - check hostname');
    }
    
    return false;
  }
}

// Test 4: Prisma connection
async function testPrismaConnection() {
  console.log('4. 🔧 Prisma Connection Test:');
  
  try {
    const { PrismaClient } = require('@prisma/client');
    console.log('   ✅ Prisma client available');
    
    const prisma = new PrismaClient({
      log: ['error', 'warn'],
    });
    
    console.log('   Testing Prisma connection...');
    await prisma.$connect();
    console.log('   ✅ Prisma connection successful!');
    
    // Test a simple query
    console.log('   Testing Prisma query...');
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('   ✅ Prisma query successful');
    
    await prisma.$disconnect();
    console.log('   🔌 Prisma connection closed');
    
    return true;
    
  } catch (error) {
    console.log('   ❌ Prisma connection failed');
    console.log('   Error:', error.message);
    return false;
  }
}

// Main test function
async function runAllTests() {
  console.log('🚀 Starting comprehensive AWS RDS connection tests...\n');
  
  const dbConnectionSuccess = await testDatabaseConnection();
  console.log('');
  
  const prismaSuccess = await testPrismaConnection();
  console.log('');
  
  // Summary
  console.log('📋 Test Results Summary:');
  console.log('========================');
  
  if (dbConnectionSuccess && prismaSuccess) {
    console.log('✅ ALL TESTS PASSED!');
    console.log('🎉 Your AWS RDS PostgreSQL database is ready for use!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Your application should now work with AWS RDS');
    console.log('2. Deploy your application to production');
    console.log('3. Run any necessary database migrations');
  } else if (dbConnectionSuccess && !prismaSuccess) {
    console.log('⚠️  PARTIAL SUCCESS: Database connection works, but Prisma has issues');
    console.log('');
    console.log('Troubleshooting:');
    console.log('1. Run: npx prisma generate');
    console.log('2. Run: npx prisma db push');
    console.log('3. Check your Prisma schema configuration');
  } else {
    console.log('❌ TESTS FAILED: Cannot connect to AWS RDS PostgreSQL');
    console.log('');
    console.log('Troubleshooting checklist:');
    console.log('1. Verify AWS RDS instance is running');
    console.log('2. Check security group allows your IP on port 5432');
    console.log('3. Verify database credentials are correct');
    console.log('4. Check network connectivity to AWS');
  }
  
  console.log('');
  console.log('Connection details:');
  console.log('- Host: interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com');
  console.log('- Port: 5432');
  console.log('- Database: postgres');
  console.log('- User: lv2srf');
}

// Run the tests
runAllTests().catch(error => {
  console.error('❌ Test execution failed:', error.message);
  process.exit(1);
});
