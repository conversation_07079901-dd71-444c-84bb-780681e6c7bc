import { NextRequest, NextResponse } from 'next/server'
import { getResearchAreaById, updateResearchArea, deleteResearchArea } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const researchArea = await getResearchAreaById(id)
    if (!researchArea) {
      return NextResponse.json(
        { error: 'Research area not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(researchArea)
  } catch (error) {
    console.error('Error fetching research area:', error)
    return NextResponse.json(
      { error: 'Failed to fetch research area' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { title, summary, lead, category, status } = body

    const success = await updateResearchArea(id, {
      title,
      summary,
      lead,
      category,
      status,
    })

    if (!success) {
      return NextResponse.json(
        { error: 'Research area not found or update failed' },
        { status: 404 }
      )
    }

    // Fetch the updated research area to return it
    const updatedResearchArea = await getResearchAreaById(id)
    return NextResponse.json(updatedResearchArea)
  } catch (error) {
    console.error('Error updating research area:', error)
    return NextResponse.json(
      { error: 'Failed to update research area' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const success = await deleteResearchArea(id)
    if (!success) {
      return NextResponse.json(
        { error: 'Research area not found or delete failed' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Research area deleted successfully' })
  } catch (error) {
    console.error('Error deleting research area:', error)
    return NextResponse.json(
      { error: 'Failed to delete research area' },
      { status: 500 }
    )
  }
}
