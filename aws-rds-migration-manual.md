# AWS RDS PostgreSQL Migration Guide

## 🎯 Overview
This guide will help you migrate your Interlock AI database from Neon PostgreSQL to AWS RDS PostgreSQL.

## 📋 Prerequisites

### 1. AWS RDS Setup
- ✅ AWS RDS PostgreSQL instance created
- ✅ Security group configured to allow your server's IP
- ✅ Database credentials ready
- ✅ Connection tested successfully

### 2. Required Tools
```bash
# Check if PostgreSQL client tools are installed
which psql
which pg_dump

# If not installed:
# Ubuntu/Debian: sudo apt-get install postgresql-client
# CentOS/RHEL: sudo yum install postgresql
# macOS: brew install postgresql
```

### 3. Connection Strings
```bash
# Current Neon Database
NEON_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# New AWS RDS Database
AWS_RDS_URL="*******************************************************************************************************/postgres?sslmode=require"
```

## 🚀 Migration Methods

### Method 1: Automated Script (Recommended)

```bash
# Make the script executable
chmod +x migrate-neon-to-aws.sh

# Update the AWS_RDS_URL in the script with your credentials
nano migrate-neon-to-aws.sh

# Run the migration
./migrate-neon-to-aws.sh
```

### Method 2: Manual Step-by-Step

#### Step 1: Export from Neon
```bash
# Export schema only
pg_dump "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" \
  --schema-only --no-owner --no-privileges > neon_schema.sql

# Export data only
pg_dump "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" \
  --data-only --no-owner --no-privileges > neon_data.sql

# Export complete database (backup)
pg_dump "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require" \
  --no-owner --no-privileges > neon_complete.sql
```

#### Step 2: Test AWS RDS Connection
```bash
# Test basic connectivity
timeout 10 bash -c "</dev/tcp/interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com/5432" && echo "✅ Connected" || echo "❌ Failed"

# Test PostgreSQL connection
psql "*******************************************************************************************************/postgres?sslmode=require" -c "SELECT version();"
```

#### Step 3: Create Schema on AWS RDS
```bash
# Option A: Use custom schema script (recommended)
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -f aws-rds-migration-schema.sql

# Option B: Use exported schema
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -f neon_schema.sql
```

#### Step 4: Import Data
```bash
# Import seed data
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -f aws-rds-migration-data.sql

# Import exported data
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -f neon_data.sql
```

#### Step 5: Verify Migration
```bash
# Check table counts
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -c "SELECT tablename, (SELECT COUNT(*) FROM pg_class WHERE relname = tablename) as exists FROM pg_tables WHERE schemaname = 'public';"

# Check specific data
psql "*******************************************************************************************************/postgres?sslmode=require" \
  -c "SELECT 'research_areas' as table_name, COUNT(*) as records FROM research_areas UNION ALL SELECT 'team_members', COUNT(*) FROM team_members;"
```

## 🔧 Update Application Configuration

### 1. Update Environment Variables
```bash
# Update .env file
DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"

# Update deployment environment
# cpanel-deployment/.env
DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"
```

### 2. Test Application Connectivity
```bash
# If Node.js is available
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.\$connect()
  .then(() => console.log('✅ Database connected!'))
  .catch(err => console.error('❌ Connection failed:', err.message))
  .finally(() => prisma.\$disconnect());
"
```

### 3. Update Deployment Scripts
Update these files with the new DATABASE_URL:
- `deploy-cpanel.js`
- `DATABASE_TROUBLESHOOTING_GUIDE.md`
- `cpanel-env-fix.js`
- Any `.env.template` files

## 🚨 Troubleshooting

### Connection Issues
```bash
# Check security groups
# - Type: PostgreSQL
# - Port: 5432
# - Source: Your server IP/32

# Check RDS instance status
# - Should be "Available"
# - Endpoint should match exactly

# Test network connectivity
telnet interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com 5432
```

### Authentication Issues
```bash
# Verify credentials
# - Username/password correct
# - Database name exists
# - SSL mode matches

# Check database exists
psql "postgresql://username:<EMAIL>:5432/postgres?sslmode=require" \
  -c "SELECT datname FROM pg_database;"
```

### Data Import Issues
```bash
# Check for conflicts
# - Duplicate data
# - Constraint violations
# - Permission issues

# Import with verbose output
psql "postgresql://..." -f data.sql -v ON_ERROR_STOP=1
```

## ✅ Post-Migration Checklist

- [ ] Database schema created successfully
- [ ] All tables exist with correct structure
- [ ] Data imported without errors
- [ ] Application connects successfully
- [ ] All features work as expected
- [ ] Environment variables updated
- [ ] Deployment scripts updated
- [ ] Backup files stored safely

## 🎉 Success!

Your Interlock AI database is now running on AWS RDS PostgreSQL! The migration should resolve the port blocking issues you experienced with your hosting provider.

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify AWS RDS security group settings
3. Test connectivity step by step
4. Check application logs for specific errors
