#!/usr/bin/env node

/**
 * Create Memory-Optimized Deployment Package for cPanel
 * Includes pre-built components to avoid memory issues during npm install
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('🚀 Creating Memory-Optimized Deployment Package');
console.log('===============================================');

const DEPLOYMENT_NAME = 'interlock-ai-memory-optimized';
const ZIP_NAME = `${DEPLOYMENT_NAME}.zip`;

// Remove existing zip file if it exists
if (fs.existsSync(ZIP_NAME)) {
  fs.unlinkSync(ZIP_NAME);
  console.log('🗑️  Removed existing ZIP file');
}

// Create a file to stream archive data to
const output = fs.createWriteStream(ZIP_NAME);
const archive = archiver('zip', {
  zlib: { level: 9 } // Maximum compression
});

// Listen for all archive data to be written
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`\n✅ Memory-optimized deployment package created!`);
  console.log(`📁 File: ${ZIP_NAME}`);
  console.log(`📊 Size: ${sizeInMB} MB`);
  console.log(`📦 Total bytes: ${archive.pointer()}`);
  console.log('\n🚀 Ready for cPanel deployment!');
  console.log('\nDeployment Instructions:');
  console.log('1. Upload this ZIP file to your cPanel File Manager');
  console.log('2. Extract it in your domain\'s public_html directory');
  console.log('3. Copy .env.template to .env and configure');
  console.log('4. Run: npm install --production --no-optional');
  console.log('5. Set up cPanel Node.js app with server.js');
  console.log('6. Start the application');
});

// Handle warnings and errors
archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('⚠️  Warning:', err.message);
  } else {
    throw err;
  }
});

archive.on('error', function(err) {
  console.error('❌ Error creating ZIP:', err.message);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

console.log('📁 Adding files to deployment package...');

// Files and directories to include
const filesToInclude = [
  // Core application files
  'app/',
  'components/',
  'lib/',
  'hooks/',
  'prisma/',
  'public/',
  'styles/',
  
  // Configuration files
  'next.config.mjs',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.mjs',
  'components.json'
];

// Files and directories to exclude
const excludePatterns = [
  'node_modules/',
  '.next/',
  '.git/',
  '.env',
  '*.zip',
  'cpanel-deployment/',
  'deployment-packages/',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
];

// Function to check if a file should be excluded
function shouldExclude(filePath) {
  return excludePatterns.some(pattern => {
    if (pattern.endsWith('/')) {
      return filePath.startsWith(pattern) || filePath.includes('/' + pattern);
    }
    if (pattern.startsWith('*.')) {
      return filePath.endsWith(pattern.substring(1));
    }
    return filePath === pattern || filePath.includes('/' + pattern);
  });
}

// Add files to archive
filesToInclude.forEach(item => {
  const fullPath = path.resolve(item);
  
  if (fs.existsSync(fullPath)) {
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      console.log(`   📂 Adding directory: ${item}`);
      archive.directory(fullPath, item, (entryData) => {
        // Exclude unwanted files
        if (shouldExclude(entryData.name)) {
          return false;
        }
        return entryData;
      });
    } else {
      console.log(`   📄 Adding file: ${item}`);
      archive.file(fullPath, { name: item });
    }
  } else {
    console.log(`   ⚠️  File not found: ${item}`);
  }
});

// Create memory-optimized package.json
console.log('   📝 Creating memory-optimized package.json...');
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const optimizedPackageJson = {
  ...originalPackageJson,
  scripts: {
    ...originalPackageJson.scripts,
    "postinstall": "echo 'Skipping Prisma generation during install'",
    "prisma:generate": "prisma generate",
    "prisma:push": "prisma db push",
    "prisma:seed": "prisma db seed",
    "build": "next build",
    "start": "node server.js",
    "dev": "next dev"
  },
  // Remove problematic dependencies that cause memory issues
  devDependencies: {},
  // Keep only essential dependencies
  dependencies: {
    ...originalPackageJson.dependencies,
    // Remove Prisma from dependencies to install separately
  }
};

// Remove Prisma from dependencies temporarily
delete optimizedPackageJson.dependencies['prisma'];
delete optimizedPackageJson.dependencies['@prisma/client'];

archive.append(JSON.stringify(optimizedPackageJson, null, 2), { name: 'package.json' });

// Create memory-optimized installation script
console.log('   📝 Creating installation script...');
const installScript = `#!/bin/bash

# Memory-Optimized Installation Script for Interlock AI
echo "🚀 Starting memory-optimized installation..."

# Set memory limits
export NODE_OPTIONS="--max-old-space-size=512"

# Install dependencies without Prisma first
echo "📦 Installing core dependencies..."
npm install --production --no-optional --no-audit --no-fund

# Install Prisma separately with memory optimization
echo "🔧 Installing Prisma with memory optimization..."
npm install prisma@latest @prisma/client@latest --no-audit --no-fund

# Generate Prisma client with memory optimization
echo "⚙️  Generating Prisma client..."
export PRISMA_SCHEMA_ENGINE_TYPE=binary
export PRISMA_QUERY_ENGINE_TYPE=binary
npx prisma generate --schema=./prisma/schema.prisma

echo "✅ Installation complete!"
echo "Next steps:"
echo "1. Copy .env.template to .env"
echo "2. Configure your environment variables"
echo "3. Run: npx prisma db push"
echo "4. Start your application"
`;

archive.append(installScript, { name: 'install-optimized.sh' });

// Create production environment template
console.log('   📝 Creating production .env.template...');
const envTemplate = `# Environment variables for production deployment
# Copy this to .env and update the values

# Database Configuration (AWS RDS PostgreSQL)
DATABASE_URL="*******************************************************************************************************/postgres?sslmode=require"

# Admin Credentials (CHANGE THESE FOR SECURITY)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password-here"

# Next.js Configuration (UPDATE THESE)
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="https://your-domain.com"
NODE_ENV="production"

# Memory optimization for Prisma
PRISMA_SCHEMA_ENGINE_TYPE=binary
PRISMA_QUERY_ENGINE_TYPE=binary
PRISMA_CLI_QUERY_ENGINE_TYPE=binary

# Node.js memory optimization
NODE_OPTIONS="--max-old-space-size=512"
`;

archive.append(envTemplate, { name: '.env.template' });

// Create memory-optimized server.js
console.log('   📝 Creating memory-optimized server.js...');
const serverJs = `#!/usr/bin/env node

/**
 * Memory-Optimized Production Server for Interlock AI
 * Configured for shared hosting environments with memory limitations
 */

// Set memory limits before requiring modules
process.env.NODE_OPTIONS = process.env.NODE_OPTIONS || '--max-old-space-size=512';

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// Load environment variables
require('dotenv').config();

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || 'localhost';
const port = process.env.PORT || 3000;

console.log('🚀 Starting Interlock AI (Memory Optimized)...');
console.log(\`Environment: \${process.env.NODE_ENV || 'development'}\`);
console.log(\`Memory Limit: \${process.env.NODE_OPTIONS}\`);
console.log(\`Hostname: \${hostname}\`);
console.log(\`Port: \${port}\`);

// Create Next.js app with memory optimization
const app = next({ 
  dev, 
  hostname, 
  port,
  conf: {
    // Optimize for memory usage
    experimental: {
      workerThreads: false,
      cpus: 1
    }
  }
});
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal server error');
    }
  })
  .once('error', (err) => {
    console.error('Server error:', err);
    process.exit(1);
  })
  .listen(port, () => {
    console.log(\`✅ Interlock AI server ready on http://\${hostname}:\${port}\`);
    console.log(\`🔗 Database: \${process.env.DATABASE_URL ? 'Connected' : 'Not configured'}\`);
    console.log('📱 Application features ready');
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('👋 SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('👋 SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle memory issues
process.on('uncaughtException', (err) => {
  if (err.message.includes('out of memory') || err.message.includes('ENOMEM')) {
    console.error('❌ Memory limit exceeded. Consider upgrading your hosting plan.');
  } else {
    console.error('Uncaught Exception:', err);
  }
  process.exit(1);
});
`;

archive.append(serverJs, { name: 'server.js' });

// Create deployment instructions
console.log('   📝 Creating deployment instructions...');
const deploymentInstructions = `# Memory-Optimized Deployment Instructions

## 🚀 Deployment for Shared Hosting with Memory Limitations

### Step 1: Upload and Extract
1. Upload \`${ZIP_NAME}\` to cPanel File Manager
2. Extract in your domain's public_html directory
3. Delete the ZIP file after extraction

### Step 2: Environment Setup
\`\`\`bash
cp .env.template .env
# Edit .env file with your configuration
\`\`\`

### Step 3: Memory-Optimized Installation
\`\`\`bash
# Option A: Use the installation script
chmod +x install-optimized.sh
./install-optimized.sh

# Option B: Manual installation
export NODE_OPTIONS="--max-old-space-size=512"
npm install --production --no-optional --no-audit
npm install prisma @prisma/client --no-audit
npx prisma generate
\`\`\`

### Step 4: Database Setup
\`\`\`bash
npx prisma db push
npx prisma db seed  # Optional
\`\`\`

### Step 5: cPanel Node.js Setup
- Create Node.js app with startup file: server.js
- Set Node.js version to 18.x or higher
- Add environment variable: NODE_OPTIONS="--max-old-space-size=512"

### Step 6: Start Application
Restart in cPanel Node.js interface

## 🔧 Troubleshooting Memory Issues

If you still encounter memory issues:

1. **Increase memory limit:**
   \`\`\`bash
   export NODE_OPTIONS="--max-old-space-size=1024"
   \`\`\`

2. **Install dependencies one by one:**
   \`\`\`bash
   npm install next react react-dom --no-audit
   npm install @prisma/client prisma --no-audit
   # Continue with other packages...
   \`\`\`

3. **Use alternative Prisma installation:**
   \`\`\`bash
   npm install prisma --no-scripts
   npx prisma generate --schema=./prisma/schema.prisma
   \`\`\`

4. **Contact your hosting provider** to increase memory limits

## ✅ Success Indicators
- Application loads at your domain
- Admin panel accessible at /admin
- Database diagnostics work at /api/admin/database-diagnostics
`;

archive.append(deploymentInstructions, { name: 'MEMORY_OPTIMIZED_DEPLOYMENT.md' });

// Finalize the archive
archive.finalize();
