// Placeholder images for CMS content when no image URL is provided

export const PLACEHOLDER_IMAGES = {
  // Feature card placeholder - tech/AI themed
  FEATURE_CARD: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop&crop=center',
  
  // Team member placeholder - professional headshot
  TEAM_MEMBER: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
  
  // Page content placeholder - abstract/tech
  PAGE_CONTENT: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=600&h=400&fit=crop&crop=center',
  
  // Research area placeholder - science/research themed
  RESEARCH_AREA: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=500&h=300&fit=crop&crop=center',
  
  // Job posting placeholder - office/work environment
  JOB_POSTING: 'https://images.unsplash.com/photo-1497366216548-37526070297c?w=500&h=300&fit=crop&crop=center',
  
  // General fallback placeholder
  GENERAL: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop&crop=center'
} as const

// Helper function to get placeholder image with fallback
export function getPlaceholderImage(type: keyof typeof PLACEHOLDER_IMAGES): string {
  return PLACEHOLDER_IMAGES[type] || PLACEHOLDER_IMAGES.GENERAL
}

// Helper function to get image URL or placeholder
export function getImageOrPlaceholder(
  imageUrl: string | null | undefined, 
  type: keyof typeof PLACEHOLDER_IMAGES
): string {
  if (imageUrl && imageUrl.trim()) {
    return imageUrl.trim()
  }
  return getPlaceholderImage(type)
}
