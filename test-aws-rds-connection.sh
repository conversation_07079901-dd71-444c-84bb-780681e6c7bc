#!/bin/bash

echo "🔍 AWS RDS PostgreSQL Connectivity Test"
echo "========================================"
echo "Target: interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com:5432"
echo "Date: $(date)"
echo ""

# Test 1: DNS Resolution
echo "1. 🌐 DNS Resolution Test:"
echo "   Testing DNS lookup..."
nslookup interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com
if [ $? -eq 0 ]; then
    echo "   ✅ DNS resolution successful"
else
    echo "   ❌ DNS resolution failed"
fi
echo ""

# Test 2: Ping Test
echo "2. 🏓 Ping Test:"
echo "   Testing ICMP connectivity..."
ping -c 3 interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com
if [ $? -eq 0 ]; then
    echo "   ✅ Ping successful"
else
    echo "   ⚠️  Ping failed (may be blocked by AWS security groups)"
fi
echo ""

# Test 3: Port Connectivity Test
echo "3. 🔌 Port 5432 Connectivity Test:"
echo "   Testing TCP connection to port 5432..."

# Method 1: Using timeout and bash TCP
timeout 10 bash -c "</dev/tcp/interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com/5432"
if [ $? -eq 0 ]; then
    echo "   ✅ Port 5432 is accessible via bash TCP"
else
    echo "   ❌ Port 5432 is not accessible via bash TCP"
fi

# Method 2: Using telnet (if available)
echo "   Testing with telnet..."
if command -v telnet &> /dev/null; then
    timeout 10 telnet interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com 5432 <<EOF
quit
EOF
    if [ $? -eq 0 ]; then
        echo "   ✅ Port 5432 is accessible via telnet"
    else
        echo "   ❌ Port 5432 is not accessible via telnet"
    fi
else
    echo "   ⚠️  telnet not available"
fi

# Method 3: Using nc (netcat) if available
echo "   Testing with netcat..."
if command -v nc &> /dev/null; then
    timeout 10 nc -zv interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com 5432
    if [ $? -eq 0 ]; then
        echo "   ✅ Port 5432 is accessible via netcat"
    else
        echo "   ❌ Port 5432 is not accessible via netcat"
    fi
else
    echo "   ⚠️  netcat not available"
fi
echo ""

# Test 4: Server Information
echo "4. 🖥️  Server Information:"
echo "   Your server's public IP:"
curl -s ifconfig.me
echo ""
echo "   Your server's location info:"
curl -s ipinfo.io/$(curl -s ifconfig.me) | grep -E '"city"|"region"|"country"'
echo ""

# Test 5: Traceroute (if available)
echo "5. 🗺️  Network Route Test:"
if command -v traceroute &> /dev/null; then
    echo "   Tracing route to AWS RDS..."
    traceroute -m 10 interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com
elif command -v tracert &> /dev/null; then
    echo "   Tracing route to AWS RDS..."
    tracert -h 10 interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com
else
    echo "   ⚠️  traceroute not available"
fi
echo ""

# Test 6: SSL Test (if openssl is available)
echo "6. 🔐 SSL Connectivity Test:"
if command -v openssl &> /dev/null; then
    echo "   Testing SSL connection..."
    timeout 10 openssl s_client -connect interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com:5432 -servername interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com < /dev/null
    if [ $? -eq 0 ]; then
        echo "   ✅ SSL connection successful"
    else
        echo "   ❌ SSL connection failed"
    fi
else
    echo "   ⚠️  openssl not available"
fi
echo ""

# Summary
echo "📋 Test Summary:"
echo "================"
echo "If all tests pass, your server can connect to AWS RDS."
echo "If port tests fail, check:"
echo "  - AWS RDS Security Groups"
echo "  - Your hosting provider's firewall"
echo "  - RDS instance status"
echo ""
echo "Next steps if connection works:"
echo "  1. Update DATABASE_URL in your .env file"
echo "  2. Test with actual PostgreSQL credentials"
echo "  3. Run application connectivity test"
