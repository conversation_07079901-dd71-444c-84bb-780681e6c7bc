"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { OptimizedImage } from "@/components/optimized-image"

export default function EmotionalIntelligencePage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto mb-12"
      >
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500">
          Emotional Intelligence
        </h1>
        <p className="text-xl text-gray-300 mb-8">
          Incorporating emotional intelligence into AI decision-making processes to enhance empathy and social
          awareness.
        </p>

        <div className="relative h-80 rounded-xl overflow-hidden border border-purple-500/30 shadow-[0_0_30px_rgba(168,85,247,0.2)] mb-12">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/60 to-cyan-900/80 z-10"></div>
          <div className="absolute inset-0 z-0">
            <OptimizedImage
              src="/placeholder.svg?height=600&width=800"
              alt="Emotional Intelligence"
              width={800}
              height={600}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Overview</h2>
            <p className="text-gray-300 mb-4">
              Emotional Intelligence research at Interlock focuses on developing AI systems that can recognize,
              understand, and appropriately respond to human emotions. Our work goes beyond simple sentiment analysis to
              create systems that can comprehend emotional nuance, empathize with users, and incorporate emotional
              considerations into decision-making processes.
            </p>
            <p className="text-gray-300">
              By enhancing AI with emotional intelligence, we're creating systems that can interact with humans in more
              natural, supportive, and socially aware ways. This research has applications across healthcare, education,
              customer service, and any domain where human-AI interaction benefits from emotional awareness.
            </p>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm mb-8">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Key Research Areas</h2>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Multimodal Emotion Recognition</h3>
                  <p className="text-gray-300">
                    Developing systems that can recognize emotions from multiple inputs, including text, voice, facial
                    expressions, and physiological signals.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Empathetic Response Generation</h3>
                  <p className="text-gray-300">
                    Creating algorithms that can generate responses that demonstrate appropriate empathy and emotional
                    awareness.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="w-6 h-6 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0 mt-1 mr-3">
                  <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white">Emotion-Aware Decision Making</h3>
                  <p className="text-gray-300">
                    Integrating emotional considerations into AI decision-making processes to enhance social awareness
                    and ethical reasoning.
                  </p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <h2 className="text-2xl font-bold text-cyan-400 mb-4">Research Team</h2>
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-cyan-500 to-purple-600 flex items-center justify-center text-white text-xl font-bold mr-4">
                JW
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white"></h3>
                <p className="text-gray-300">Lead Researcher, Emotional Intelligence</p>
              </div>
            </div>
            <p className="text-gray-300">
              Our interdisciplinary team combines expertise in psychology, affective computing, neuroscience, and AI to
              develop systems with enhanced emotional intelligence. We collaborate with experts in human emotion and
              social interaction to ensure our models accurately reflect the complexity of human emotional experience.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
