import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Seed Research Areas
  const researchAreas = [
    {
      title: "Neural Associative Modeling",
      summary: "Creating AI systems that form associations between concepts similar to human cognitive processes, enabling more intuitive learning and reasoning.",
      lead: "Dr. <PERSON>",
      status: "in-progress",
      category: "cognitive",
    },
    {
      title: "Memory Graph Prioritization",
      summary: "Developing algorithms that prioritize information based on relevance, recency, and emotional significance, similar to human memory systems.",
      lead: "Dr. <PERSON>",
      status: "published",
      category: "memory",
    },
    {
      title: "Contextual Understanding Frameworks",
      summary: "Building systems that comprehend nuanced contexts and adapt responses accordingly, improving human-AI interactions.",
      lead: "Dr. <PERSON>",
      status: "published",
      category: "cognitive",
    },
    {
      title: "Emotional Intelligence Integration",
      summary: "Incorporating emotional intelligence into AI decision-making processes to enhance empathy and social awareness in automated systems.",
      lead: "Dr. <PERSON>",
      status: "in-progress",
      category: "emotional",
    },
  ]

  // Check if research areas already exist
  const existingResearchCount = await prisma.research_areas.count()
  if (existingResearchCount === 0) {
    for (const area of researchAreas) {
      await prisma.research_areas.create({
        data: area,
      })
    }
    console.log('✅ Research areas seeded')
  }

  // Seed Feature Cards
  const featureCards = [
    {
      title: "Neural Associative Modeling",
      description: "Creating AI systems that form associations between concepts similar to human cognitive processes.",
      icon: "🧠",
      image: "/placeholder.svg?height=400&width=600",
      order_index: 1,
    },
    {
      title: "Memory Graph Prioritization",
      description: "Developing algorithms that prioritize information based on relevance and emotional significance.",
      icon: "🔄",
      image: "/placeholder.svg?height=400&width=600",
      order_index: 2,
    },
    {
      title: "Contextual Understanding",
      description: "Building systems that comprehend nuanced contexts and adapt responses accordingly.",
      icon: "🔍",
      image: "/placeholder.svg?height=400&width=600",
      order_index: 3,
    },
  ]

  // Check if feature cards already exist
  const existingCardsCount = await prisma.feature_cards.count()
  if (existingCardsCount === 0) {
    for (const card of featureCards) {
      await prisma.feature_cards.create({
        data: card,
      })
    }
    console.log('✅ Feature cards seeded')
  }

  // Seed Team Members
  const teamMembers = [
    {
      name: "Dr. Alex Johnson",
      position: "CEO & Co-Founder",
      bio: "Leading expert in cognitive AI with 15+ years of experience in neuroscience and machine learning.",
      initials: "AJ",
      order_index: 1,
      is_active: true,
      image: "/placeholder.svg?height=300&width=300",
    },
    {
      name: "Dr. Maria Lopez",
      position: "CTO & Co-Founder",
      bio: "Former Google AI researcher specializing in neural network architectures and human-computer interaction.",
      initials: "ML",
      order_index: 2,
      is_active: true,
      image: "/placeholder.svg?height=300&width=300",
    },
    {
      name: "Dr. David Chen",
      position: "Head of Research",
      bio: "PhD in Cognitive Psychology with expertise in memory systems and associative learning algorithms.",
      initials: "DC",
      order_index: 3,
      is_active: true,
      image: "/placeholder.svg?height=300&width=300",
    },
  ]

  // Check if team members already exist
  const existingMembersCount = await prisma.team_members.count()
  if (existingMembersCount === 0) {
    for (const member of teamMembers) {
      await prisma.team_members.create({
        data: member,
      })
    }
    console.log('✅ Team members seeded')
  }

  // Seed Job Postings
  const jobPostings = [
    {
      title: "Senior AI Researcher",
      department: "Research",
      location: "San Francisco, CA",
      type: "Full-time",
      description: "Join our research team to develop cutting-edge human-inspired AI models focusing on neural associative modeling and contextual understanding.",
      requirements: [
        "Ph.D. in Computer Science, Cognitive Science, or related field",
        "5+ years of experience in AI/ML research",
        "Strong publication record in top-tier conferences",
        "Experience with neural network architectures and deep learning frameworks",
      ],
      is_active: true,
    },
    {
      title: "Machine Learning Engineer",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
      description: "Build and deploy scalable AI systems that implement our research breakthroughs in production environments.",
      requirements: [
        "Master's degree in Computer Science or related field",
        "3+ years of experience in ML engineering",
        "Proficiency in Python, TensorFlow/PyTorch",
        "Experience with cloud platforms (AWS, GCP, Azure)",
      ],
      is_active: true,
    },
  ]

  // Check if job postings already exist
  const existingJobsCount = await prisma.job_postings.count()
  if (existingJobsCount === 0) {
    for (const job of jobPostings) {
      await prisma.job_postings.create({
        data: job,
      })
    }
    console.log('✅ Job postings seeded')
  }

  // Seed Company Info
  const companyInfo = [
    {
      section: "mission",
      title: "Our Mission",
      content: "At Interlock, we're on a mission to create artificial intelligence that thinks more like humans do. We believe that by modeling AI systems after human cognitive processes, we can develop technology that's more intuitive, adaptable, and aligned with human values and needs.",
      order_index: 1,
    },
    {
      section: "story",
      title: "Our Story",
      content: "Interlock was founded in 2020 by a team of researchers who saw the limitations of conventional AI approaches. While traditional AI excelled at pattern recognition and statistical analysis, it often failed to capture the nuanced, contextual, and associative nature of human thought.",
      order_index: 2,
    },
    {
      section: "values",
      title: "Our Values",
      content: "We believe in transparency, ethical AI development, and the importance of human-centered design. Our work is guided by the principle that AI should augment human capabilities rather than replace them.",
      order_index: 3,
    },
  ]

  // Check if company info already exists
  const existingCompanyCount = await prisma.company_info.count()
  if (existingCompanyCount === 0) {
    for (const info of companyInfo) {
      await prisma.company_info.create({
        data: info,
      })
    }
    console.log('✅ Company info seeded')
  }

  // Seed Page Content
  const pageContent = [
    {
      page_id: "homepage",
      section_id: "hero",
      title: "Artificial Human-Inspired Learning",
      content: "Pioneering the next generation of AI through human-inspired cognitive models and neural associative frameworks.",
      order_index: 1,
      is_active: true,
      image: "/placeholder.svg?height=600&width=800",
    },
    {
      page_id: "homepage",
      section_id: "about",
      title: "Redefining AI Through Human Cognition",
      content: "At Interlock LLC, we're not just building artificial intelligence—we're creating systems that learn, adapt, and reason with human-inspired cognitive frameworks.",
      order_index: 2,
      is_active: true,
      image: "/placeholder.svg?height=600&width=800",
    },
  ]

  // Check if page content already exists
  const existingContentCount = await prisma.page_content.count()
  if (existingContentCount === 0) {
    for (const content of pageContent) {
      await prisma.page_content.create({
        data: content,
      })
    }
    console.log('✅ Page content seeded')
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
