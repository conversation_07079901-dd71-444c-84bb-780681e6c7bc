"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { 
  RefreshCw, 
  Database, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Server, 
  Table,
  AlertTriangle,
  Info
} from "lucide-react"

interface DatabaseDiagnostics {
  timestamp: string
  database: {
    provider: string
    url: string
    urlMasked: string | null
  }
  connection: {
    status: 'connected' | 'failed' | 'unknown'
    error: string | null
    latency: number | null
  }
  models: {
    available: string[]
    counts: Record<string, number | string>
  }
  environment: {
    nodeEnv: string
    prismaVersion: string | null
  }
}

export function DatabaseDiagnostics() {
  const [diagnostics, setDiagnostics] = useState<DatabaseDiagnostics | null>(null)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const runDiagnostics = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/database-diagnostics')
      if (!response.ok) {
        throw new Error('Failed to fetch diagnostics')
      }
      const data = await response.json()
      setDiagnostics(data)
      
      toast({
        title: "Diagnostics Complete",
        description: `Database status: ${data.connection.status}`,
        variant: data.connection.status === 'connected' ? 'default' : 'destructive',
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runDiagnostics()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-400" />
      default:
        return <AlertTriangle className="w-5 h-5 text-yellow-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">Connected</Badge>
      case 'failed':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">Failed</Badge>
      default:
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">Unknown</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-cyan-400">Database Diagnostics</h2>
          <p className="text-gray-400">Monitor database connection and performance</p>
        </div>
        <Button
          onClick={runDiagnostics}
          disabled={loading}
          className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-600 hover:to-purple-700"
        >
          {loading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Run Diagnostics
        </Button>
      </div>

      {diagnostics && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {/* Connection Status */}
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Database className="w-5 h-5 text-cyan-400" />
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Status:</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(diagnostics.connection.status)}
                  {getStatusBadge(diagnostics.connection.status)}
                </div>
              </div>
              
              {diagnostics.connection.latency && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Latency:</span>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-cyan-400" />
                    <span className="text-cyan-400">{diagnostics.connection.latency}ms</span>
                  </div>
                </div>
              )}
              
              {diagnostics.connection.error && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{diagnostics.connection.error}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Database Info */}
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Server className="w-5 h-5 text-cyan-400" />
                Database Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Provider:</span>
                <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                  {diagnostics.database.provider}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-300">URL Status:</span>
                <Badge className={diagnostics.database.url === 'configured' 
                  ? "bg-green-500/20 text-green-400 border-green-500/30"
                  : "bg-red-500/20 text-red-400 border-red-500/30"
                }>
                  {diagnostics.database.url}
                </Badge>
              </div>
              
              {diagnostics.database.urlMasked && (
                <div className="bg-gray-800/50 rounded-lg p-3">
                  <p className="text-xs text-gray-400 mb-1">Connection String:</p>
                  <p className="text-sm text-cyan-400 font-mono break-all">
                    {diagnostics.database.urlMasked}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Environment Info */}
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Info className="w-5 h-5 text-cyan-400" />
                Environment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Node Environment:</span>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                  {diagnostics.environment.nodeEnv}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Prisma Version:</span>
                <span className="text-cyan-400 font-mono text-sm">
                  {diagnostics.environment.prismaVersion || 'unknown'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Last Check:</span>
                <span className="text-gray-400 text-sm">
                  {new Date(diagnostics.timestamp).toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Models & Tables */}
          <Card className="border-purple-500/20 bg-gray-900/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Table className="w-5 h-5 text-cyan-400" />
                Database Models
              </CardTitle>
              <CardDescription>
                Available models and record counts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {diagnostics.models.available.length > 0 ? (
                  diagnostics.models.available.map((model) => (
                    <div key={model} className="flex items-center justify-between py-2 border-b border-gray-700/50 last:border-b-0">
                      <span className="text-gray-300 font-medium">{model}</span>
                      <Badge variant="outline" className="text-xs">
                        {diagnostics.models.counts[model] !== undefined 
                          ? diagnostics.models.counts[model] 
                          : 'unknown'} records
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-400 text-sm">No models found</p>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
