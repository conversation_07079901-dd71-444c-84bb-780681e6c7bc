# Manual AWS RDS Connection Test Commands
# Run these commands one by one in your PuTTY session

# 1. Test DNS Resolution
echo "Testing DNS resolution..."
nslookup interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com

# 2. Test Basic Connectivity
echo "Testing basic connectivity..."
ping -c 3 interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com

# 3. Test Port 5432 Access (Method 1 - Bash TCP)
echo "Testing port 5432 with bash..."
timeout 10 bash -c "</dev/tcp/interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com/5432" && echo "✅ Port 5432 accessible" || echo "❌ Port 5432 blocked"

# 4. Test Port 5432 Access (Method 2 - Telnet)
echo "Testing port 5432 with telnet..."
telnet interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com 5432

# 5. Test Port 5432 Access (Method 3 - Netcat)
echo "Testing port 5432 with netcat..."
nc -zv interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com 5432

# 6. Check Your Server's IP
echo "Your server's public IP:"
curl ifconfig.me

# 7. Test with curl
echo "Testing with curl..."
curl -v --connect-timeout 10 telnet://interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com:5432

# Expected Results:
# ✅ SUCCESS: Connection established, you'll see connection messages
# ❌ FAILURE: Connection timeout, refused, or "No route to host"

# If successful, you should see something like:
# "Connected to interlock-aws-postgresql.ckbeums4622v.us-east-1.rds.amazonaws.com"

# Common Error Messages and Meanings:
# "Connection timed out" = Firewall blocking or wrong endpoint
# "Connection refused" = Service not running or port closed
# "No route to host" = Network routing issue
# "Name or service not known" = DNS resolution failed
