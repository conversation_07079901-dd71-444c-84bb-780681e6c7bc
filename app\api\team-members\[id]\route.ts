import { NextRequest, NextResponse } from 'next/server'
import { getTeamMemberById, updateTeamMember, deleteTeamMember } from '@/lib/database'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const teamMember = await getTeamMemberById(id)
    if (!teamMember) {
      return NextResponse.json(
        { error: 'Team member not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(teamMember)
  } catch (error) {
    console.error('Error fetching team member:', error)
    return NextResponse.json(
      { error: 'Failed to fetch team member' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, position, bio, image, initials, order_index, is_active } = body

    // Use placeholder avatar if no image provided
    const placeholderAvatar = 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'

    const success = await updateTeamMember(id, {
      name,
      position,
      bio,
      image: image && image.trim() ? image : placeholderAvatar,
      initials,
      order_index,
      is_active,
    })

    if (!success) {
      return NextResponse.json(
        { error: 'Team member not found or update failed' },
        { status: 404 }
      )
    }

    // Fetch the updated team member to return it
    const updatedTeamMember = await getTeamMemberById(id)
    return NextResponse.json(updatedTeamMember)
  } catch (error) {
    console.error('Error updating team member:', error)
    return NextResponse.json(
      { error: 'Failed to update team member' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Invalid ID' },
        { status: 400 }
      )
    }

    const success = await deleteTeamMember(id)
    if (!success) {
      return NextResponse.json(
        { error: 'Team member not found or delete failed' },
        { status: 404 }
      )
    }

    return NextResponse.json({ message: 'Team member deleted successfully' })
  } catch (error) {
    console.error('Error deleting team member:', error)
    return NextResponse.json(
      { error: 'Failed to delete team member' },
      { status: 500 }
    )
  }
}
