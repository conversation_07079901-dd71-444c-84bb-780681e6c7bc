#!/usr/bin/env node

/**
 * Prisma Client Fix Script for Server
 * 
 * This script fixes Prisma client initialization issues on the server.
 * It handles various edge cases and provides detailed error messages.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Prisma Client Fix Script 🔧');
console.log('==============================\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found in current directory');
  console.error('Please run this script from your application root directory');
  process.exit(1);
}

// Check if prisma schema exists
if (!fs.existsSync('prisma/schema.prisma')) {
  console.error('❌ Error: prisma/schema.prisma not found');
  console.error('Please make sure your Prisma schema file exists');
  process.exit(1);
}

// Check if .env file exists
if (!fs.existsSync('.env')) {
  console.log('⚠️ Warning: .env file not found');
  console.log('Creating .env file from template...');
  
  if (fs.existsSync('.env.template')) {
    fs.copyFileSync('.env.template', '.env');
    console.log('✅ Created .env file from template');
    console.log('⚠️ Please edit .env file with your actual database credentials');
  } else {
    console.log('⚠️ No .env.template found. Creating basic .env file...');
    const basicEnv = `# Database connection
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=public"

# Next.js
NODE_ENV=production

# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password
`;
    fs.writeFileSync('.env', basicEnv);
    console.log('✅ Created basic .env file');
    console.log('⚠️ Please edit .env file with your actual database credentials');
  }
}

// Create .npmrc file with legacy-peer-deps
console.log('📝 Creating .npmrc file with legacy-peer-deps flag...');
fs.writeFileSync('.npmrc', 'legacy-peer-deps=true\n');
console.log('✅ Created .npmrc file');

// Clean up any existing Prisma client
console.log('🧹 Cleaning up any existing Prisma client...');
try {
  if (fs.existsSync('node_modules/.prisma')) {
    fs.rmSync('node_modules/.prisma', { recursive: true, force: true });
  }
  if (fs.existsSync('node_modules/@prisma/client')) {
    fs.rmSync('node_modules/@prisma/client', { recursive: true, force: true });
  }
  console.log('✅ Cleaned up existing Prisma client');
} catch (error) {
  console.log('⚠️ Warning: Could not clean up existing Prisma client');
  console.log('   This is common due to file permissions');
  console.log('   Continuing with installation...');
}

// Install Prisma client
console.log('📦 Installing Prisma client...');
try {
  execSync('npm install @prisma/client', { stdio: 'inherit' });
  console.log('✅ Installed Prisma client');
} catch (error) {
  console.error('❌ Error installing Prisma client');
  console.error('Trying with --force flag...');
  try {
    execSync('npm install @prisma/client --force', { stdio: 'inherit' });
    console.log('✅ Installed Prisma client with --force flag');
  } catch (forceError) {
    console.error('❌ Error installing Prisma client even with --force flag');
    console.error('Please check your npm configuration and try again');
  }
}

// Generate Prisma client
console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Generated Prisma client successfully');
} catch (error) {
  console.error('❌ Error generating Prisma client');
  console.error('Trying with additional options...');
  
  // Set Prisma binary targets
  process.env.PRISMA_CLI_BINARY_TARGETS = 'linux-openssl-1.1.x';
  process.env.PRISMA_ENGINES_MIRROR = 'https://binaries.prisma.sh';
  
  try {
    execSync('npx prisma generate', { stdio: 'inherit', env: process.env });
    console.log('✅ Generated Prisma client successfully with binary targets');
  } catch (binaryError) {
    console.error('❌ Error generating Prisma client even with binary targets');
    console.error('Trying with --schema flag...');
    
    try {
      execSync('npx prisma generate --schema=./prisma/schema.prisma', { stdio: 'inherit', env: process.env });
      console.log('✅ Generated Prisma client successfully with --schema flag');
    } catch (schemaError) {
      console.error('❌ Error generating Prisma client with all options');
      console.error('Please check your database connection and Prisma schema');
      
      // Create a troubleshooting file
      const troubleshooting = `# Prisma Client Troubleshooting

## Error Generating Prisma Client

You're seeing this file because the script was unable to generate the Prisma client.
Here are some things to check:

1. **Database Connection**
   - Make sure your DATABASE_URL in .env is correct
   - Check that your database is accessible from this server
   - Verify your database credentials

2. **Prisma Schema**
   - Make sure your schema.prisma file is valid
   - Check for any syntax errors

3. **Server Environment**
   - Make sure you have sufficient permissions
   - Check that you have enough disk space
   - Verify that Node.js is installed correctly

4. **Manual Steps**
   - Try running \`npx prisma generate\` manually
   - Check the error messages for more details

5. **Contact Support**
   - If you continue to have issues, please contact support
`;
      fs.writeFileSync('PRISMA_TROUBLESHOOTING_MANUAL.md', troubleshooting);
      console.error('📝 Created PRISMA_TROUBLESHOOTING_MANUAL.md with troubleshooting steps');
      process.exit(1);
    }
  }
}

// Verify Prisma client installation
console.log('🔍 Verifying Prisma client installation...');
if (fs.existsSync('node_modules/.prisma/client') && fs.existsSync('node_modules/@prisma/client')) {
  console.log('✅ Prisma client installed and generated successfully');
} else {
  console.log('⚠️ Warning: Prisma client may not be installed correctly');
  console.log('   Please check the error messages above');
}

console.log('\n✅ Prisma client fix complete!');
console.log('🔄 Please restart your Node.js application in cPanel');
console.log('   Go to cPanel > Setup Node.js App > Select your app > Click "Restart"');
