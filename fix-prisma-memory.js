#!/usr/bin/env node

/**
 * Prisma Memory Fix Script
 * 
 * This script helps fix the "Out of memory: wasm memory" error when running Prisma on servers with limited memory.
 * It configures Prisma to use binary engines instead of WebAssembly, which requires less memory.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Prisma Memory Fix Script 🔧');
console.log('==============================\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found in current directory');
  console.error('Please run this script from your application root directory');
  process.exit(1);
}

// Check if prisma schema exists
if (!fs.existsSync('prisma/schema.prisma')) {
  console.error('❌ Error: prisma/schema.prisma not found');
  console.error('Please make sure your Prisma schema file exists');
  process.exit(1);
}

// Create .env file with binary engine configuration
console.log('📝 Creating .env file with binary engine configuration...');
const envContent = `# Database connection
DATABASE_URL="${process.env.DATABASE_URL || "postgresql://username:password@hostname:port/database?schema=public"}"

# Next.js
NODE_ENV=production

# Prisma configuration - use binary engine instead of WASM
PRISMA_SCHEMA_ENGINE_TYPE=binary
PRISMA_QUERY_ENGINE_TYPE=binary
PRISMA_CLI_QUERY_ENGINE_TYPE=binary

# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=512"
`;

fs.writeFileSync('.env', envContent);
console.log('✅ Created .env file with binary engine configuration');

// Create .npmrc file with legacy-peer-deps
console.log('📝 Creating .npmrc file with legacy-peer-deps flag...');
fs.writeFileSync('.npmrc', 'legacy-peer-deps=true\n');
console.log('✅ Created .npmrc file');

// Clean up any existing Prisma client
console.log('🧹 Cleaning up any existing Prisma client...');
try {
  if (fs.existsSync('node_modules/.prisma')) {
    fs.rmSync('node_modules/.prisma', { recursive: true, force: true });
  }
  if (fs.existsSync('node_modules/@prisma/client')) {
    fs.rmSync('node_modules/@prisma/client', { recursive: true, force: true });
  }
  console.log('✅ Cleaned up existing Prisma client');
} catch (error) {
  console.log('⚠️ Warning: Could not clean up existing Prisma client');
  console.log('   This is common due to file permissions');
  console.log('   Continuing with installation...');
}

// Install Prisma client with reduced memory usage
console.log('📦 Installing Prisma client with reduced memory usage...');
try {
  // Set environment variables for the child process
  const env = {
    ...process.env,
    PRISMA_SCHEMA_ENGINE_TYPE: 'binary',
    PRISMA_QUERY_ENGINE_TYPE: 'binary',
    PRISMA_CLI_QUERY_ENGINE_TYPE: 'binary',
    NODE_OPTIONS: '--max-old-space-size=512'
  };
  
  execSync('npm install @prisma/client --no-save', { 
    stdio: 'inherit',
    env
  });
  console.log('✅ Installed Prisma client');
} catch (error) {
  console.error('❌ Error installing Prisma client');
  console.error('Trying with --force flag...');
  try {
    execSync('npm install @prisma/client --no-save --force', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        PRISMA_SCHEMA_ENGINE_TYPE: 'binary',
        PRISMA_QUERY_ENGINE_TYPE: 'binary',
        PRISMA_CLI_QUERY_ENGINE_TYPE: 'binary',
        NODE_OPTIONS: '--max-old-space-size=512'
      }
    });
    console.log('✅ Installed Prisma client with --force flag');
  } catch (forceError) {
    console.error('❌ Error installing Prisma client even with --force flag');
    console.error('Please check your npm configuration and try again');
  }
}

// Generate Prisma client with reduced memory usage
console.log('🔧 Generating Prisma client with reduced memory usage...');
try {
  execSync('npx prisma generate', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      PRISMA_SCHEMA_ENGINE_TYPE: 'binary',
      PRISMA_QUERY_ENGINE_TYPE: 'binary',
      PRISMA_CLI_QUERY_ENGINE_TYPE: 'binary',
      NODE_OPTIONS: '--max-old-space-size=512'
    }
  });
  console.log('✅ Generated Prisma client successfully');
} catch (error) {
  console.error('❌ Error generating Prisma client');
  console.error('Please try running the following command manually:');
  console.error('PRISMA_SCHEMA_ENGINE_TYPE=binary PRISMA_QUERY_ENGINE_TYPE=binary NODE_OPTIONS="--max-old-space-size=512" npx prisma generate');
}

console.log('\n✅ Prisma memory fix complete!');
console.log('🔄 Please restart your Node.js application in cPanel');
console.log('   Go to cPanel > Setup Node.js App > Select your app > Click "Restart"');
