/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Disable symbolic links to prevent Windows/OneDrive issues
  outputFileTracingRoot: process.cwd(),

  // Additional webpack configuration to handle OneDrive/Windows file system issues
  webpack: (config, { dev, isServer }) => {
    // Disable file system caching in development to prevent OneDrive conflicts
    if (dev) {
      config.cache = false;
    }

    // Configure file watching to ignore problematic directories
    config.watchOptions = {
      ...config.watchOptions,
      ignored: [
        '**/node_modules/**',
        '**/.next/**',
        '**/deployment-packages/**',
        '**/*.zip',
        '**/ManualDeployment.zip',
        '**/interlock-ai-deployment*.zip'
      ],
      // Reduce polling to prevent file system overload
      poll: dev ? 1000 : false,
    };

    // Disable symlinks resolution
    config.resolve.symlinks = false;

    // Remove or disable problematic plugins in development
    if (dev) {
      config.plugins = config.plugins.filter(plugin => {
        // Filter out copy-file-plugin that's causing the issue
        return !plugin.constructor.name.includes('CopyFilePlugin');
      });
    }

    // Add fallback for file system operations
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
    };

    return config;
  },

  // Experimental features to improve Windows compatibility
  experimental: {
    // Disable webpack build worker to prevent file access conflicts
    webpackBuildWorker: false,
  },
}

export default nextConfig
