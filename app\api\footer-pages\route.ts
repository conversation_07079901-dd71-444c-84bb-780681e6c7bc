import { NextRequest, NextResponse } from 'next/server'

// Mock data for footer pages
let footerPages = [
  {
    id: 1,
    category: 'research' as const,
    title: 'Neural Associative Modeling',
    slug: 'neural-associative-modeling',
    content: `# Neural Associative Modeling

Our research in Neural Associative Modeling focuses on developing AI systems that can form and utilize complex associations between concepts, similar to how the human brain processes information.

## Key Research Areas

- **Memory Formation**: Understanding how artificial neural networks can form lasting memories
- **Association Patterns**: Mapping the relationships between different concepts and ideas
- **Contextual Learning**: Developing systems that learn from context and experience

## Current Projects

We are currently working on several groundbreaking projects that push the boundaries of what's possible in neural associative modeling.

## Publications

Our research has been published in leading AI journals and conferences worldwide.`,
    meta_description: 'Explore our cutting-edge research in Neural Associative Modeling and AI memory systems.',
    is_published: true,
    order_index: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 2,
    category: 'company' as const,
    title: 'About Us',
    slug: 'about',
    content: `# About Interlock AI

Interlock AI is at the forefront of artificial intelligence research, developing human-inspired cognitive models that push the boundaries of what's possible in AI.

## Our Mission

To create AI systems that think, learn, and adapt like humans, bridging the gap between artificial and human intelligence.

## Our Vision

A future where AI and humans work together seamlessly, enhancing human capabilities and solving the world's most complex challenges.

## Our Values

- **Innovation**: We constantly push the boundaries of AI research
- **Ethics**: We develop AI responsibly and transparently
- **Collaboration**: We believe in the power of human-AI collaboration`,
    meta_description: 'Learn about Interlock AI, our mission, vision, and commitment to advancing artificial intelligence.',
    is_published: true,
    order_index: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 3,
    category: 'legal' as const,
    title: 'Privacy Policy',
    slug: 'privacy-policy',
    content: `# Privacy Policy

Last updated: January 1, 2024

## Information We Collect

We collect information you provide directly to us, such as when you create an account, use our services, or contact us.

## How We Use Your Information

We use the information we collect to provide, maintain, and improve our services.

## Information Sharing

We do not sell, trade, or otherwise transfer your personal information to third parties without your consent.

## Data Security

We implement appropriate security measures to protect your personal information.

## Contact Us

If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.`,
    meta_description: 'Read our privacy policy to understand how we collect, use, and protect your personal information.',
    is_published: true,
    order_index: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    
    let filteredPages = footerPages
    if (category) {
      filteredPages = footerPages.filter(page => page.category === category)
    }
    
    return NextResponse.json(filteredPages)
  } catch (error) {
    console.error('Error fetching footer pages:', error)
    return NextResponse.json({ error: 'Failed to fetch footer pages' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    const newPage = {
      id: Math.max(...footerPages.map(p => p.id), 0) + 1,
      ...data,
      created_at: new Date(),
      updated_at: new Date()
    }
    
    footerPages.push(newPage)
    
    return NextResponse.json(newPage, { status: 201 })
  } catch (error) {
    console.error('Error creating footer page:', error)
    return NextResponse.json({ error: 'Failed to create footer page' }, { status: 500 })
  }
}
