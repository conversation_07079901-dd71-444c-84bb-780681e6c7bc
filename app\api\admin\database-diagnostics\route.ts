import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      database: {
        provider: 'postgresql',
        url: process.env.DATABASE_URL ? 'configured' : 'not configured',
        urlMasked: process.env.DATABASE_URL ? maskDatabaseUrl(process.env.DATABASE_URL) : null,
      },
      connection: {
        status: 'unknown',
        error: null,
        latency: null,
      },
      models: {
        available: [],
        counts: {},
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        prismaVersion: null,
      }
    }

    // Test database connection
    const startTime = Date.now()
    try {
      await prisma.$connect()
      const endTime = Date.now()
      diagnostics.connection.status = 'connected'
      diagnostics.connection.latency = endTime - startTime

      // Test a simple query
      await prisma.$queryRaw`SELECT 1 as test`
      
      // Get available models
      const models = Object.keys(prisma).filter(key => 
        !key.startsWith('$') && 
        !key.startsWith('_') && 
        typeof prisma[key as keyof typeof prisma] === 'object'
      )
      
      diagnostics.models.available = models

      // Get record counts for each model (safely)
      for (const modelName of models) {
        try {
          const model = prisma[modelName as keyof typeof prisma] as any
          if (model && typeof model.count === 'function') {
            const count = await model.count()
            diagnostics.models.counts[modelName] = count
          }
        } catch (error) {
          diagnostics.models.counts[modelName] = 'error'
        }
      }

    } catch (error: any) {
      diagnostics.connection.status = 'failed'
      diagnostics.connection.error = error.message
    } finally {
      await prisma.$disconnect()
    }

    // Get Prisma version
    try {
      const { version } = require('@prisma/client/package.json')
      diagnostics.environment.prismaVersion = version
    } catch (error) {
      diagnostics.environment.prismaVersion = 'unknown'
    }

    return NextResponse.json(diagnostics)
  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to run diagnostics', details: error.message },
      { status: 500 }
    )
  }
}

function maskDatabaseUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    const masked = `${urlObj.protocol}//${urlObj.username ? '***:***@' : ''}${urlObj.hostname}${urlObj.port ? ':' + urlObj.port : ''}${urlObj.pathname}${urlObj.search}`
    return masked
  } catch (error) {
    return 'invalid url format'
  }
}
