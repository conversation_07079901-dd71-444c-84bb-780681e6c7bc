"use client"

import { useEffect, useRef } from "react"

export function CircuitAnimation() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      const dpr = window.devicePixelRatio || 1
      canvas.width = window.innerWidth * dpr
      canvas.height = window.innerHeight * dpr
      canvas.style.width = `${window.innerWidth}px`
      canvas.style.height = `${window.innerHeight}px`
      ctx.scale(dpr, dpr)
    }

    setCanvasDimensions()

    // Throttle resize events for better performance
    let resizeTimeout: NodeJS.Timeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        setCanvasDimensions()
      }, 100)
    }

    window.addEventListener("resize", handleResize)

    // Optimize node count based on screen size
    const nodeCount = Math.min(
      Math.floor((window.innerWidth * window.innerHeight) / 20000),
      100, // Cap at 100 nodes for performance
    )

    // Circuit node class
    class Node {
      x: number
      y: number
      radius: number
      color: string
      connections: Node[]
      pulseRadius: number
      pulseAlpha: number
      isPulsing: boolean

      constructor(x: number, y: number) {
        this.x = x
        this.y = y
        this.radius = Math.random() * 2 + 1
        this.color = `rgb(${Math.floor(Math.random() * 100 + 100)}, ${Math.floor(Math.random() * 100 + 100)}, ${Math.floor(Math.random() * 155 + 100)})`
        this.connections = []
        this.pulseRadius = 0
        this.pulseAlpha = 0
        this.isPulsing = false
      }

      draw() {
        if (!ctx) return

        // Draw node
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
        ctx.fillStyle = this.color
        ctx.fill()

        // Draw connections
        this.connections.forEach((node) => {
          ctx.beginPath()
          ctx.moveTo(this.x, this.y)
          ctx.lineTo(node.x, node.y)
          ctx.strokeStyle = `rgba(${this.color.slice(4, -1)}, 0.2)`
          ctx.lineWidth = 0.5
          ctx.stroke()
        })

        // Draw pulse
        if (this.isPulsing) {
          ctx.beginPath()
          ctx.arc(this.x, this.y, this.pulseRadius, 0, Math.PI * 2)
          ctx.strokeStyle = `rgba(${this.color.slice(4, -1)}, ${this.pulseAlpha})`
          ctx.lineWidth = 1
          ctx.stroke()

          this.pulseRadius += 1
          this.pulseAlpha -= 0.01

          if (this.pulseAlpha <= 0) {
            this.isPulsing = false
            this.pulseRadius = 0
            this.pulseAlpha = 0
          }
        }
      }

      pulse() {
        this.isPulsing = true
        this.pulseRadius = this.radius
        this.pulseAlpha = 0.5
      }
    }

    // Create nodes
    const nodes: Node[] = []

    for (let i = 0; i < nodeCount; i++) {
      const x = (Math.random() * canvas.width) / window.devicePixelRatio
      const y = (Math.random() * canvas.height) / window.devicePixelRatio
      nodes.push(new Node(x, y))
    }

    // Connect nodes
    nodes.forEach((node) => {
      const connectionCount = Math.floor(Math.random() * 3) + 1

      for (let i = 0; i < connectionCount; i++) {
        const randomNode = nodes[Math.floor(Math.random() * nodes.length)]
        if (randomNode !== node && !node.connections.includes(randomNode)) {
          node.connections.push(randomNode)
        }
      }
    })

    // Use requestAnimationFrame with a throttle for better performance
    let animationFrameId: number
    let lastFrameTime = 0
    const targetFPS = 30
    const frameInterval = 1000 / targetFPS

    const animate = (timestamp: number) => {
      if (!ctx) return

      const elapsed = timestamp - lastFrameTime

      if (elapsed > frameInterval) {
        lastFrameTime = timestamp - (elapsed % frameInterval)

        ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)

        nodes.forEach((node) => {
          node.draw()
        })

        // Randomly pulse nodes (less frequently for better performance)
        if (Math.random() < 0.02) {
          const randomNode = nodes[Math.floor(Math.random() * nodes.length)]
          randomNode.pulse()
        }
      }

      animationFrameId = requestAnimationFrame(animate)
    }

    animationFrameId = requestAnimationFrame(animate)

    return () => {
      window.removeEventListener("resize", handleResize)
      cancelAnimationFrame(animationFrameId)
    }
  }, [])

  return <canvas ref={canvasRef} className="w-full h-full" />
}
