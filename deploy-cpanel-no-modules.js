#!/usr/bin/env node

/**
 * cPanel Deployment Preparation Script for Next.js App (No node_modules)
 * This script prepares your Next.js application for deployment to cPanel hosting
 * without including the node_modules folder
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting cPanel deployment preparation (without node_modules)...\n');

// Configuration
const DEPLOYMENT_DIR = 'cpanel-deployment-no-modules';
const REQUIRED_FILES = [
  'package.json',
  'next.config.mjs',
  'prisma',
  'public',
  'app',
  'components',
  'lib',
  'hooks',
  'styles',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.mjs',
  'components.json'
];

// Create deployment directory
console.log(`📁 Creating deployment directory: ${DEPLOYMENT_DIR}`);
if (fs.existsSync(DEPLOYMENT_DIR)) {
  console.log('   Directory already exists, cleaning...');
  fs.rmSync(DEPLOYMENT_DIR, { recursive: true, force: true });
}
fs.mkdirSync(DEPLOYMENT_DIR, { recursive: true });

// Create .npmrc file with legacy-peer-deps flag
console.log('📝 Creating .npmrc file with legacy-peer-deps flag...');
fs.writeFileSync(path.join(DEPLOYMENT_DIR, '.npmrc'), 'legacy-peer-deps=true\n');

// Create .env.template file
console.log('📝 Creating .env.template file...');
const envTemplate = `# Database connection
DATABASE_URL="postgresql://username:password@hostname:port/database?schema=public"

# Next.js
NODE_ENV=production

# Admin credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-secure-password

# Prisma configuration - use binary engine instead of WASM to prevent memory issues
PRISMA_SCHEMA_ENGINE_TYPE=binary
PRISMA_QUERY_ENGINE_TYPE=binary
PRISMA_CLI_QUERY_ENGINE_TYPE=binary

# Increase Node.js memory limit
NODE_OPTIONS="--max-old-space-size=512"
`;
fs.writeFileSync(path.join(DEPLOYMENT_DIR, '.env.template'), envTemplate);

// Generate Prisma client (attempt but don't fail if it doesn't work)
console.log('🔧 Generating Prisma client...');
try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully');
} catch (error) {
  console.log('⚠️  Warning: Prisma client generation failed, will generate on server');
  console.log('   This is common on Windows due to file permissions');
  console.log('   A postinstall script has been added to package.json to ensure Prisma client is generated on the server');
}

// Check for existing build
console.log('🔨 Checking for existing build...');
if (fs.existsSync('.next')) {
  console.log('✅ Build already exists, skipping build step');
} else {
  console.log('🔨 Building the application...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
  }
}

// Copy required files
console.log('📋 Copying required files and directories...');
REQUIRED_FILES.forEach(file => {
  const sourcePath = path.resolve(file);
  const destPath = path.join(DEPLOYMENT_DIR, file);

  if (fs.existsSync(sourcePath)) {
    if (fs.lstatSync(sourcePath).isDirectory()) {
      console.log(`   Copying directory: ${file}`);
      fs.cpSync(sourcePath, destPath, { recursive: true });
    } else {
      console.log(`   Copying file: ${file}`);
      fs.copyFileSync(sourcePath, destPath);
    }
  } else {
    console.log(`   ⚠️ Warning: ${file} not found, skipping`);
  }
});

// Copy Prisma fix scripts
console.log('📋 Copying Prisma fix scripts...');
const prismaFixScripts = [
  'server-fix-prisma.js',
  'check-prisma-status.js',
  'test-prisma-connection.js',
  'fix-prisma-memory.js',
  'PRISMA_TROUBLESHOOTING.md',
  'CPANEL_PRISMA_GUIDE.md',
  'PRISMA_FIX_INSTRUCTIONS.md',
  'MEMORY_FIX_GUIDE.md',
  'DEPLOYMENT_INSTRUCTIONS_UPDATED.md'
];

prismaFixScripts.forEach(file => {
  const sourcePath = path.resolve(file);
  const destPath = path.join(DEPLOYMENT_DIR, file);

  if (fs.existsSync(sourcePath)) {
    console.log(`   Copying file: ${file}`);
    fs.copyFileSync(sourcePath, destPath);
  } else {
    console.log(`   ⚠️ Warning: ${file} not found, skipping`);
  }
});

// Copy .next directory
console.log('📋 Copying .next directory...');
fs.cpSync('.next', path.join(DEPLOYMENT_DIR, '.next'), { recursive: true });

// Add dotenv dependency for test-prisma-connection.js
console.log('📝 Adding dotenv dependency for test scripts...');
const packageJsonPath = path.join(DEPLOYMENT_DIR, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (!packageJson.dependencies.dotenv) {
    packageJson.dependencies.dotenv = "^16.3.1";
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('   ✅ Added dotenv dependency to package.json');
  } else {
    console.log('   ✅ dotenv dependency already exists in package.json');
  }
} else {
  console.log('   ⚠️ Warning: package.json not found in deployment directory');
}

// Create deployment instructions
console.log('📝 Creating deployment instructions...');
const deploymentInstructions = `# Interlock AI - cPanel Deployment Guide

## Quick Start

1. Upload all files to your cPanel hosting
2. Install dependencies: \`npm install --production\`
3. Generate Prisma client: \`npx prisma generate\`
4. Set up environment variables in .env file
5. Start the application using cPanel Node.js app interface

## Detailed Instructions

### 1. Prepare Your cPanel Environment

- Log in to your cPanel account
- Navigate to "Setup Node.js App" in the Software section
- Create a new Node.js application:
  - Application mode: Production
  - Node.js version: 20.x or higher
  - Application root: Your public_html or subdirectory
  - Application URL: Your domain or subdomain
  - Application startup file: server.js

### 2. Upload Files

- Upload all files from this package to your application directory
- Make sure to preserve the directory structure

### 3. Install Dependencies

- In cPanel terminal or SSH, navigate to your app directory
- Run: npm install --production

### 4. Environment Setup

- Copy .env.template to .env
- Edit .env with your actual database credentials and settings

### 5. Database Setup
- In cPanel terminal or SSH, navigate to your app directory
- Run: npx prisma generate (to generate Prisma client)
- Run: npx prisma db push (to sync database schema)
- Optional: npx prisma db seed (to seed initial data)

### 6. Start the Application
- Click "Restart" in the Node.js interface
- Your app should now be running!

## CloudLinux Specific Notes
- node_modules is managed by cPanel NodeJS Selector
- Do not upload node_modules directory
- Dependencies are installed in a virtual environment
- Use cPanel interface for package management

## Troubleshooting

If you encounter any issues:

1. Check the application logs in cPanel
2. Verify your database connection string in .env
3. Make sure all required dependencies are installed
4. Ensure the Prisma client is generated properly
5. Check that your Node.js version is compatible (20.x+)

For more help, contact your hosting provider or refer to the Next.js documentation.
`;
fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'DEPLOYMENT_INSTRUCTIONS.md'), deploymentInstructions);

// Copy updated deployment instructions if available
if (fs.existsSync('DEPLOYMENT_INSTRUCTIONS_UPDATED.md')) {
  console.log('📋 Copying updated deployment instructions...');
  fs.copyFileSync('DEPLOYMENT_INSTRUCTIONS_UPDATED.md', path.join(DEPLOYMENT_DIR, 'DEPLOYMENT_INSTRUCTIONS.md'));
}

// Create server.js file for cPanel
console.log('📝 Creating server.js file for cPanel...');
const serverJs = `// cPanel Node.js server file
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = process.env.PORT || 3000;

// Initialize Next.js
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('Internal Server Error');
    }
  }).listen(port, (err) => {
    if (err) throw err;
    console.log(\`> Ready on http://\${hostname}:\${port}\`);
  });
});
`;
fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'server.js'), serverJs);

// Create deployment checklist
console.log('📝 Creating deployment checklist...');
const deploymentChecklist = `# Deployment Checklist

Use this checklist to ensure all steps are completed for a successful deployment.

## Pre-Deployment

- [ ] Application builds successfully locally
- [ ] All environment variables are documented in .env.template
- [ ] Database schema is finalized
- [ ] All required assets are included

## cPanel Setup

- [ ] cPanel Node.js app is created
- [ ] Node.js version is set to 20.x or higher
- [ ] Application root directory is configured
- [ ] Application URL is set correctly

## File Upload

- [ ] All application files are uploaded
- [ ] Directory structure is preserved
- [ ] File permissions are set correctly (typically 644 for files, 755 for directories)

## Configuration

- [ ] .env file is created from template
- [ ] Database connection string is correct
- [ ] Admin credentials are set
- [ ] Production environment is configured

## Database Setup

- [ ] Generate Prisma client: \`npx prisma generate\`
- [ ] Deploy database schema: \`npx prisma db push\`
- [ ] Seed database if needed: \`npx prisma db seed\`
- [ ] Verify database connection

## Application Launch

- [ ] Install production dependencies: \`npm install --production\`
- [ ] Start the application (click "Restart" in cPanel)
- [ ] Verify application status shows "Running"
- [ ] Check for any startup errors in logs

## Post-Deployment Verification

### Website Functionality
- [ ] Homepage loads correctly
- [ ] All pages are accessible
- [ ] Navigation works properly
- [ ] Images and static assets load
- [ ] Responsive design works on mobile
- [ ] Contact form functions (if applicable)

### Admin Functionality
- [ ] Admin login works
- [ ] Content management features work
- [ ] Data can be created/updated/deleted

## Performance & Security

- [ ] Website loads quickly
- [ ] No console errors
- [ ] Admin area is secure
- [ ] Sensitive data is protected

## Backup

- [ ] Database backup is created
- [ ] Application files are backed up
`;
fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'DEPLOYMENT_CHECKLIST.md'), deploymentChecklist);

// Create a README file
console.log('📝 Creating README file...');
const readmeContent = `# Interlock AI - cPanel Deployment Package

This package contains all the files needed to deploy the Interlock AI application to cPanel hosting.

## Important Files

- \`DEPLOYMENT_INSTRUCTIONS.md\` - Step-by-step guide for deployment
- \`DEPLOYMENT_CHECKLIST.md\` - Checklist to ensure all steps are completed
- \`.env.template\` - Template for environment variables
- \`server.js\` - Custom server for cPanel

## Deployment Quick Start

1. Upload all files to your cPanel hosting
2. Install dependencies: \`npm install --production\`
3. Generate Prisma client: \`npx prisma generate\`
4. Set up environment variables in .env file
5. Start the application using cPanel Node.js app interface

For detailed instructions, please refer to the DEPLOYMENT_INSTRUCTIONS.md file.

## Note About node_modules

This deployment package does not include the node_modules directory, as cPanel's NodeJS Selector manages dependencies in a virtual environment. You will need to run \`npm install --production\` on the server to install the required dependencies.
`;
fs.writeFileSync(path.join(DEPLOYMENT_DIR, 'README.md'), readmeContent);

console.log('\n✅ Deployment preparation complete!');
console.log(`📦 Files are ready in the ${DEPLOYMENT_DIR} directory`);
console.log('🔍 Next steps:');
console.log('   1. Create a ZIP archive of the deployment directory');
console.log('   2. Upload the ZIP to your cPanel hosting');
console.log('   3. Follow the instructions in DEPLOYMENT_INSTRUCTIONS.md');
